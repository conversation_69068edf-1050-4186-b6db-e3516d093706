<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>二维码扫描功能演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 32px;
        }
        .header h1 {
            color: #333;
            margin-bottom: 8px;
        }
        .header p {
            color: #666;
            margin: 0;
        }
        .demo-section {
            margin-bottom: 32px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .demo-section h3 {
            margin-top: 0;
            color: #333;
        }
        .button-group {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
            margin-bottom: 16px;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }
        .btn-primary {
            background-color: #007AFF;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056CC;
        }
        .btn-secondary {
            background-color: #f0f0f0;
            color: #333;
        }
        .btn-secondary:hover {
            background-color: #e0e0e0;
        }
        .result-area {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 16px;
            margin-top: 16px;
            min-height: 60px;
        }
        .result-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        .result-content {
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 13px;
            color: #666;
            white-space: pre-wrap;
            word-break: break-all;
        }
        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #007AFF;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .error {
            color: #dc3545;
        }
        .success {
            color: #28a745;
        }
        .history-item {
            padding: 12px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .history-item:last-child {
            border-bottom: none;
        }
        .history-data {
            flex: 1;
            font-family: monospace;
            font-size: 13px;
            margin-right: 12px;
            word-break: break-all;
        }
        .history-time {
            font-size: 12px;
            color: #999;
            white-space: nowrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 二维码扫描功能演示</h1>
            <p>测试 WebView 与原生二维码扫描功能的集成</p>
        </div>

        <!-- 基础扫描功能 -->
        <div class="demo-section">
            <h3>📱 基础扫描功能</h3>
            <div class="button-group">
                <button class="btn btn-primary" onclick="basicScan()">
                    开始扫描
                </button>
                <button class="btn btn-secondary" onclick="scanWithOptions()">
                    自定义扫描
                </button>
                <button class="btn btn-secondary" onclick="clearResult('basic-result')">
                    清除结果
                </button>
            </div>
            <div class="result-area">
                <div class="result-title">扫描结果：</div>
                <div id="basic-result" class="result-content">等待扫描...</div>
            </div>
        </div>

        <!-- 批量扫描功能 -->
        <div class="demo-section">
            <h3>📋 批量扫描功能</h3>
            <div class="button-group">
                <button class="btn btn-primary" onclick="multipleScan()">
                    批量扫描
                </button>
                <button class="btn btn-secondary" onclick="clearResult('multiple-result')">
                    清除结果
                </button>
            </div>
            <div class="result-area">
                <div class="result-title">批量扫描结果：</div>
                <div id="multiple-result" class="result-content">等待扫描...</div>
            </div>
        </div>

        <!-- 扫描历史 -->
        <div class="demo-section">
            <h3>📚 扫描历史</h3>
            <div class="button-group">
                <button class="btn btn-secondary" onclick="clearHistory()">
                    清除历史
                </button>
                <button class="btn btn-secondary" onclick="exportHistory()">
                    导出历史
                </button>
            </div>
            <div class="result-area">
                <div class="result-title">历史记录：</div>
                <div id="scan-history" class="result-content">暂无记录</div>
            </div>
        </div>

        <!-- 实用工具 -->
        <div class="demo-section">
            <h3>🛠 实用工具</h3>
            <div class="button-group">
                <button class="btn btn-secondary" onclick="testConnection()">
                    测试连接
                </button>
                <button class="btn btn-secondary" onclick="showDeviceInfo()">
                    设备信息
                </button>
            </div>
            <div class="result-area">
                <div class="result-title">工具结果：</div>
                <div id="tool-result" class="result-content">点击按钮测试功能...</div>
            </div>
        </div>
    </div>

    <!-- 引入必要的 JavaScript 文件 -->
    <script src="../js/native-bridge.js"></script>
    <script src="../js/qrcode-api.js"></script>

    <script>
        // 扫描历史存储
        let scanHistory = JSON.parse(localStorage.getItem('qr_scan_history') || '[]');

        // 基础扫描功能
        async function basicScan() {
            const resultDiv = document.getElementById('basic-result');
            
            try {
                showLoading(resultDiv, '正在启动扫描...');
                
                const result = await QRCodeAPI.scan({
                    prompt: '请将二维码对准扫描框',
                    enableFlash: true,
                    timeout: 30000
                });
                
                const displayResult = {
                    data: result.data,
                    format: result.format,
                    timestamp: result.timestamp,
                    type: detectDataType(result.data)
                };
                
                showResult(resultDiv, displayResult, 'success');
                addToHistory(result.data, result.format);
                
                // 根据扫描结果类型执行相应操作
                handleScanResult(result.data);
                
            } catch (error) {
                showResult(resultDiv, `扫描失败: ${error.message}`, 'error');
            }
        }

        // 自定义选项扫描
        async function scanWithOptions() {
            const resultDiv = document.getElementById('basic-result');
            
            try {
                showLoading(resultDiv, '正在启动自定义扫描...');
                
                const result = await QRCodeAPI.scan({
                    prompt: '自定义扫描 - 支持多种格式',
                    enableFlash: true,
                    timeout: 45000,
                    formats: ['QR_CODE', 'CODE_128', 'CODE_39', 'EAN_13']
                });
                
                const displayResult = {
                    data: result.data,
                    format: result.format,
                    timestamp: result.timestamp,
                    scanType: '自定义扫描',
                    supportedFormats: ['QR_CODE', 'CODE_128', 'CODE_39', 'EAN_13']
                };
                
                showResult(resultDiv, displayResult, 'success');
                addToHistory(result.data, result.format);
                
            } catch (error) {
                showResult(resultDiv, `自定义扫描失败: ${error.message}`, 'error');
            }
        }

        // 批量扫描功能
        async function multipleScan() {
            const resultDiv = document.getElementById('multiple-result');
            
            try {
                showLoading(resultDiv, '正在启动批量扫描...');
                
                const result = await QRCodeAPI.scanMultiple({
                    maxCount: 5,
                    timeout: 120000,
                    interval: 1000
                });
                
                const displayResult = {
                    totalCount: result.count,
                    results: result.results,
                    timestamp: new Date().toISOString()
                };
                
                showResult(resultDiv, displayResult, 'success');
                
                // 将所有结果添加到历史记录
                result.results.forEach(item => {
                    addToHistory(item.data, item.format);
                });
                
            } catch (error) {
                showResult(resultDiv, `批量扫描失败: ${error.message}`, 'error');
            }
        }

        // 检测数据类型
        function detectDataType(data) {
            if (data.startsWith('http://') || data.startsWith('https://')) {
                return 'URL';
            } else if (data.includes('@') && data.includes('.')) {
                return 'Email';
            } else if (data.startsWith('tel:')) {
                return 'Phone';
            } else if (data.startsWith('wifi:')) {
                return 'WiFi';
            } else if (/^\d+$/.test(data)) {
                return 'Number';
            } else {
                return 'Text';
            }
        }

        // 处理扫描结果
        function handleScanResult(data) {
            const type = detectDataType(data);
            
            switch (type) {
                case 'URL':
                    if (confirm(`检测到网址，是否打开？\n${data}`)) {
                        window.open(data, '_blank');
                    }
                    break;
                case 'Email':
                    if (confirm(`检测到邮箱地址，是否发送邮件？\n${data}`)) {
                        window.location.href = `mailto:${data}`;
                    }
                    break;
                case 'Phone':
                    if (confirm(`检测到电话号码，是否拨打？\n${data}`)) {
                        window.location.href = data;
                    }
                    break;
                default:
                    console.log('扫描到文本内容:', data);
            }
        }

        // 添加到历史记录
        function addToHistory(data, format) {
            const historyItem = {
                data: data,
                format: format,
                timestamp: new Date().toISOString(),
                type: detectDataType(data)
            };
            
            scanHistory.unshift(historyItem);
            
            // 限制历史记录数量
            if (scanHistory.length > 50) {
                scanHistory = scanHistory.slice(0, 50);
            }
            
            localStorage.setItem('qr_scan_history', JSON.stringify(scanHistory));
            updateHistoryDisplay();
        }

        // 更新历史记录显示
        function updateHistoryDisplay() {
            const historyDiv = document.getElementById('scan-history');
            
            if (scanHistory.length === 0) {
                historyDiv.innerHTML = '暂无记录';
                return;
            }
            
            const historyHtml = scanHistory.slice(0, 10).map(item => `
                <div class="history-item">
                    <div class="history-data">
                        <strong>[${item.type}]</strong> ${item.data.substring(0, 50)}${item.data.length > 50 ? '...' : ''}
                    </div>
                    <div class="history-time">${new Date(item.timestamp).toLocaleString()}</div>
                </div>
            `).join('');
            
            historyDiv.innerHTML = historyHtml;
        }

        // 清除历史记录
        function clearHistory() {
            if (confirm('确定要清除所有扫描历史吗？')) {
                scanHistory = [];
                localStorage.removeItem('qr_scan_history');
                updateHistoryDisplay();
            }
        }

        // 导出历史记录
        function exportHistory() {
            if (scanHistory.length === 0) {
                alert('没有历史记录可导出');
                return;
            }
            
            const csvContent = 'data:text/csv;charset=utf-8,' + 
                '时间,类型,格式,内容\n' +
                scanHistory.map(item => 
                    `"${item.timestamp}","${item.type}","${item.format}","${item.data.replace(/"/g, '""')}"`
                ).join('\n');
            
            const encodedUri = encodeURI(csvContent);
            const link = document.createElement('a');
            link.setAttribute('href', encodedUri);
            link.setAttribute('download', `qr_scan_history_${new Date().toISOString().split('T')[0]}.csv`);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 测试连接
        async function testConnection() {
            const resultDiv = document.getElementById('tool-result');
            
            try {
                showLoading(resultDiv, '正在测试原生桥接连接...');
                
                // 测试基础连接
                const testResult = await window.nativeBridge.callNative('device.getDeviceInfo', {});
                
                showResult(resultDiv, {
                    status: '连接正常',
                    bridge: '原生桥接可用',
                    deviceInfo: testResult,
                    timestamp: new Date().toISOString()
                }, 'success');
                
            } catch (error) {
                showResult(resultDiv, `连接测试失败: ${error.message}`, 'error');
            }
        }

        // 显示设备信息
        async function showDeviceInfo() {
            const resultDiv = document.getElementById('tool-result');
            
            try {
                showLoading(resultDiv, '正在获取设备信息...');
                
                const deviceInfo = await window.nativeBridge.callNative('device.getDeviceInfo', {});
                const deviceId = await window.nativeBridge.callNative('device.getDeviceId', {});
                
                showResult(resultDiv, {
                    deviceId: deviceId,
                    deviceInfo: deviceInfo,
                    userAgent: navigator.userAgent,
                    screenSize: `${screen.width}x${screen.height}`,
                    timestamp: new Date().toISOString()
                }, 'success');
                
            } catch (error) {
                showResult(resultDiv, `获取设备信息失败: ${error.message}`, 'error');
            }
        }

        // 显示加载状态
        function showLoading(element, message) {
            element.innerHTML = `<span class="loading"></span>${message}`;
            element.className = 'result-content';
        }

        // 显示结果
        function showResult(element, result, type = 'success') {
            const resultText = typeof result === 'object' ? 
                JSON.stringify(result, null, 2) : result;
            
            element.innerHTML = resultText;
            element.className = `result-content ${type}`;
        }

        // 清除结果
        function clearResult(elementId) {
            const element = document.getElementById(elementId);
            element.innerHTML = '等待操作...';
            element.className = 'result-content';
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateHistoryDisplay();
            
            // 设置页面标题
            if (window.NavigationAPI) {
                window.NavigationAPI.setTitle('二维码扫描演示');
            }
            
            console.log('二维码扫描演示页面已加载');
        });

        // 监听原生事件
        window.onNativeEvent = function(event, data) {
            console.log('收到原生事件:', event, data);
            
            switch (event) {
                case 'scan_result':
                    // 处理扫描结果事件
                    break;
                case 'scan_error':
                    // 处理扫描错误事件
                    break;
            }
        };
    </script>
</body>
</html>