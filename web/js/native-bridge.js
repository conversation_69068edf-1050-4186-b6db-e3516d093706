// 原生桥接核心类
class NativeBridge {
  constructor() {
    this.callbackId = 0;
    this.callbacks = {};
    window.NativeCallbacks = this.callbacks;
  }

  // 调用原生方法的通用函数
  callNative(method, params = {}) {
    return new Promise((resolve, reject) => {
      const callbackId = `callback_${++this.callbackId}`;

      // 注册回调函数
      this.callbacks[callbackId] = (result) => {
        delete this.callbacks[callbackId]; // 清理回调
        if (result.success) {
          resolve(result.data);
        } else {
          reject(new Error(result.error));
        }
      };

      // 调用原生方法
      const message = {
        method,
        params,
        callbackId
      };

      // 设置超时处理
      setTimeout(() => {
        if (this.callbacks[callbackId]) {
          delete this.callbacks[callbackId];
          reject(new Error('Native call timeout'));
        }
      }, 30000);

      if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
        window.flutter_inappwebview.callHandler('nativeMethodCall', message);
      } else if (window.NativeAPI && window.NativeAPI.postMessage) {
        window.NativeAPI.postMessage(JSON.stringify(message));
      } else {
        reject(new Error('Native API not available'));
      }
    });
  }
}

// 全局实例
window.nativeBridge = new NativeBridge();