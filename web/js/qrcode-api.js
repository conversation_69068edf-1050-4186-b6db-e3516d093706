// 二维码功能接口
window.QRCodeAPI = {
  // 扫描二维码
  scan: async (options = {}) => {
    try {
      const params = {
        prompt: options.prompt || '请将二维码放入扫描框内',
        enableFlash: options.enableFlash !== false,
        timeout: options.timeout || 30000,
        formats: options.formats || ['QR_CODE', 'CODE_128', 'CODE_39'] // 支持的格式
      };
      
      const result = await window.nativeBridge.callNative('qrcode.scan', params);
      return {
        success: true,
        data: result.data,
        format: result.format,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      throw new Error(`二维码扫描失败: ${error.message}`);
    }
  },
  
  // 生成二维码
  generate: async (data, options = {}) => {
    try {
      const params = {
        data: data,
        size: options.size || 200,
        errorCorrectionLevel: options.errorCorrectionLevel || 'M',
        margin: options.margin || 1,
        backgroundColor: options.backgroundColor || '#FFFFFF',
        foregroundColor: options.foregroundColor || '#000000'
      };
      
      const result = await window.nativeBridge.callNative('qrcode.generate', params);
      return {
        success: true,
        imagePath: result.imagePath,
        base64: result.base64
      };
    } catch (error) {
      throw new Error(`二维码生成失败: ${error.message}`);
    }
  },

  // 批量扫描（连续扫描多个二维码）
  scanMultiple: async (options = {}) => {
    try {
      const params = {
        maxCount: options.maxCount || 10,
        timeout: options.timeout || 60000,
        interval: options.interval || 1000 // 扫描间隔
      };
      
      const result = await window.nativeBridge.callNative('qrcode.scanMultiple', params);
      return {
        success: true,
        results: result.results,
        count: result.count
      };
    } catch (error) {
      throw new Error(`批量扫描失败: ${error.message}`);
    }
  }
};

// 兼容性API（保持向后兼容）
window.scanQRCode = async (callback) => {
  try {
    const result = await window.QRCodeAPI.scan();
    if (callback) callback(result.data);
    return result.data;
  } catch (error) {
    console.error('扫码失败:', error);
    if (callback) callback(null);
    throw error;
  }
};