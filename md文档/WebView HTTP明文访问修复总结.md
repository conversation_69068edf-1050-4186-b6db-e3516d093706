# WebView HTTP明文访问修复总结

## 问题描述

用户反馈工作台进入详情页面时加载失败，错误信息为：
```
net::ERR_CLEARTEXT_NOT_PERMITTED
```

这是Android 9（API 28）及以上版本的安全限制，默认不允许应用使用HTTP明文连接。

## 问题分析

### 1. 错误原因
- **Android安全策略**：从Android 9开始，默认禁用HTTP明文流量
- **网络安全配置问题**：现有配置使用了不支持的CIDR表示法
- **缺少备用方案**：没有HTTPS备用加载机制

### 2. 具体场景
- 工作台应用URL：`/apps/calendar.html`
- 转换后的完整URL：`http://************:8080/apps/calendar.html`
- 错误：Android系统阻止HTTP明文连接

## 修复方案

### 1. 修复网络安全配置

**问题**：原配置使用了不支持的CIDR表示法
```xml
<!-- 错误的配置 -->
<domain includeSubdomains="true">***********/16</domain>
<domain includeSubdomains="true">**********/12</domain>
<domain includeSubdomains="true">10.0.0.0/8</domain>
```

**修复**：使用具体的IP地址和全局配置
```xml
<!-- 修复后的配置 -->
<domain-config cleartextTrafficPermitted="true">
    <!-- 本地开发地址 -->
    <domain includeSubdomains="true">localhost</domain>
    <domain includeSubdomains="true">127.0.0.1</domain>
    <domain includeSubdomains="true">********</domain>
    
    <!-- 常见内网IP段 -->
    <domain includeSubdomains="true">***********</domain>
    <domain includeSubdomains="true">***********00</domain>
    <domain includeSubdomains="true">************</domain>
    <!-- 更多内网IP... -->
</domain-config>

<!-- 全局配置 - 允许所有HTTP流量（开发环境） -->
<base-config cleartextTrafficPermitted="true">
    <trust-anchors>
        <certificates src="system"/>
    </trust-anchors>
</base-config>
```

### 2. 添加HTTPS备用方案

在WebView代码中添加智能错误处理：

```dart
onWebResourceError: (WebResourceError error) {
  // 检查是否是HTTP明文不允许的错误
  if (error.description.contains('ERR_CLEARTEXT_NOT_PERMITTED')) {
    debugPrint('WebViewScreen: 检测到HTTP明文不允许错误，尝试HTTPS');
    _tryHttpsAlternative();
    return;
  }
  // 其他错误处理...
},
```

**HTTPS备用方案实现**：
```dart
Future<void> _tryHttpsAlternative() async {
  try {
    String originalUrl = widget.url;
    String httpsUrl;
    
    // 如果原始URL是相对路径，先转换为完整URL
    if (originalUrl.startsWith('/')) {
      final baseUrl = await ApiService.getBaseUrl();
      final serverUrl = baseUrl.replaceAll('/api', '');
      httpsUrl = serverUrl.replaceFirst('http://', 'https://') + originalUrl;
    } else if (originalUrl.startsWith('http://')) {
      // 直接将HTTP替换为HTTPS
      httpsUrl = originalUrl.replaceFirst('http://', 'https://');
    } else {
      // 其他情况，添加HTTPS协议
      httpsUrl = 'https://$originalUrl';
    }
    
    debugPrint('WebViewScreen: 尝试HTTPS备用方案 - 原始: $originalUrl, HTTPS: $httpsUrl');
    
    final uri = Uri.tryParse(httpsUrl);
    if (uri != null && uri.hasScheme && uri.hasAuthority) {
      _controller?.loadRequest(uri);
    } else {
      throw FormatException('HTTPS URL格式无效: $httpsUrl');
    }
    
  } catch (e) {
    debugPrint('WebViewScreen: HTTPS备用方案失败 - $e');
    // 显示错误页面
  }
}
```

## 技术细节

### 1. Android网络安全配置

**文件位置**：`android/app/src/main/res/xml/network_security_config.xml`

**配置说明**：
- `cleartextTrafficPermitted="true"`：允许HTTP明文流量
- `domain-config`：针对特定域名的配置
- `base-config`：全局默认配置
- `includeSubdomains="true"`：包含子域名

### 2. AndroidManifest.xml配置

确保以下配置存在：
```xml
<application
    android:networkSecurityConfig="@xml/network_security_config"
    android:usesCleartextTraffic="true">
```

### 3. 智能协议选择策略

1. **HTTP明文失败时**：自动尝试HTTPS
2. **相对路径处理**：
   - 原始：`/apps/calendar.html`
   - HTTP：`http://************:8080/apps/calendar.html`
   - HTTPS备用：`https://************:8080/apps/calendar.html`

3. **完整URL处理**：
   - 原始：`http://example.com/page`
   - HTTPS备用：`https://example.com/page`

## 修复效果

### 1. 支持的场景
- ✅ **内网HTTP访问**：通过网络安全配置允许
- ✅ **HTTPS备用**：HTTP失败时自动尝试HTTPS
- ✅ **开发环境**：localhost、127.0.0.1等本地地址
- ✅ **企业内网**：192.168.x.x、172.16.x.x、10.x.x.x等

### 2. 错误处理
- **智能重试**：HTTP失败自动尝试HTTPS
- **友好提示**：两种协议都失败时显示清晰错误信息
- **调试支持**：详细的日志记录便于问题排查

### 3. 兼容性
- **Android版本**：支持Android 9+的安全限制
- **网络环境**：同时支持HTTP和HTTPS
- **开发生产**：开发环境允许HTTP，生产环境优先HTTPS

## 使用建议

### 1. 开发环境
- 使用当前配置，允许HTTP明文访问
- 便于内网开发和测试

### 2. 生产环境
建议修改网络安全配置：
```xml
<!-- 生产环境配置 -->
<base-config cleartextTrafficPermitted="false">
    <trust-anchors>
        <certificates src="system"/>
    </trust-anchors>
</base-config>
```

### 3. 服务器配置
- **推荐**：服务器同时支持HTTP和HTTPS
- **备选**：只支持HTTPS，依赖自动重试机制

## 验证方法

### 1. 测试HTTP访问
1. 进入工作台页面
2. 点击任意应用（如日程管理）
3. 观察是否能正常加载页面

### 2. 测试HTTPS备用
1. 临时禁用HTTP访问
2. 验证是否自动切换到HTTPS
3. 检查日志中的重试信息

### 3. 错误处理测试
1. 使用无效的URL
2. 验证错误页面显示
3. 确认错误信息清晰友好

## 总结

通过修复Android网络安全配置和添加HTTPS备用方案，现在WebView可以：

1. **正常访问HTTP内网地址**：解决了`ERR_CLEARTEXT_NOT_PERMITTED`错误
2. **智能协议切换**：HTTP失败时自动尝试HTTPS
3. **完善错误处理**：提供友好的错误提示和调试信息
4. **保持安全性**：在允许开发便利的同时保持必要的安全限制

这个修复确保了工作台应用的详情页面能够正常加载，同时为未来的HTTPS迁移提供了平滑的过渡方案。
