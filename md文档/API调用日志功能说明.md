# API调用日志功能说明

## 概述

根据用户需求，我已经成功创建了一个新的"API调用日志"页面，用于查看所有页面的API调用记录和调试信息。该功能与错误日志页面保持一致的格式样式，支持主题切换和语言切换。

## 功能特点

### 1. 页面位置
- 在设置页面中，位于"错误日志"之后
- 菜单项显示为"API调用日志"（中文）或"API Call Logs"（英文）
- 副标题：查看所有页面的API调用记录和调试信息

### 2. 主要功能
- **查看API日志**：显示所有API请求和响应的详细信息
- **日志分类**：按照日志标签（API_REQUEST、API_RESPONSE、API、LOGIN等）进行分类
- **统计信息**：显示各类型日志的数量统计
- **过滤功能**：点击统计标签可以过滤显示特定类型的日志
- **复制功能**：可以复制单条日志或所有过滤后的日志
- **折叠展开**：默认折叠显示摘要，点击可展开查看详细信息
- **测试功能**：提供测试按钮生成示例API日志
- **清除日志**：支持清除所有API调用日志

### 3. 界面设计
- **空状态**：当没有API日志时显示友好的空状态界面
- **日志统计卡片**：显示各类型日志的数量，支持点击过滤
- **过滤头部**：当激活过滤时显示当前过滤条件
- **日志列表**：以卡片形式显示每条日志的摘要信息
- **折叠展开设计**：
  - 默认显示：日志类型、时间、请求方法、URL、状态码
  - 展开显示：完整的请求和响应详细信息
  - 点击卡片或展开按钮可切换显示状态
- **颜色区分**：
  - API请求：蓝色边框和背景
  - API响应：绿色边框和背景
  - 登录接口：橙色边框和背景
  - 状态码颜色：绿色(2xx)、橙色(4xx)、红色(5xx)

### 4. 多语言支持
所有界面文本都支持中英文切换：

#### 中文翻译
- API调用日志
- 查看所有页面的API调用记录和调试信息
- API请求、API响应
- 请求方法、请求地址、请求头、请求体
- 响应状态、响应数据、调用时间
- 暂无API调用日志
- API日志已清除
- 复制API日志、查看API日志、清除API日志
- API日志已复制到剪贴板

#### 英文翻译
- API Call Logs
- View API call records and debug information from all pages
- API Request、API Response
- Request Method、Request URL、Request Headers、Request Body
- Response Status、Response Data、Call Time
- No API call logs
- API logs cleared
- Copy API Log、View API Log、Clear API Logs
- API log copied to clipboard

### 5. 主题支持
- 完全支持浅色和深色主题切换
- 使用AppTheme的主题感知颜色方法
- 卡片颜色、边框颜色、文本颜色都会根据主题自动调整

## 技术实现

### 1. 文件结构
```
lib/screens/api_call_log_screen.dart  # API调用日志页面
lib/services/debug_log_service.dart   # 调试日志服务（已存在）
lib/services/localization_service.dart # 多语言服务（已更新）
lib/screens/settings_screen.dart      # 设置页面（已更新）
```

### 2. 核心功能
- **日志获取**：从DebugLogService获取API相关的日志
- **日志过滤**：支持按标签过滤日志（API_REQUEST、API_RESPONSE等）
- **日志统计**：统计各类型日志的数量
- **日志显示**：以用户友好的方式显示日志信息
- **日志操作**：支持复制、查看详情、清除等操作

### 3. 数据来源
API调用日志来自于现有的DebugLogService，该服务已经在以下位置记录API调用信息：
- ApiService中的API请求和响应
- 登录接口调用
- 网络错误处理
- HTTP客户端请求

## 使用方法

### 1. 访问页面
1. 打开应用
2. 进入"设置"页面
3. 找到"API调用日志"菜单项
4. 点击进入API调用日志页面

### 2. 查看日志
- 页面会自动加载所有API相关的日志
- 日志按时间倒序排列（最新的在前面）
- 每条日志显示类型、消息摘要和时间

### 3. 过滤日志
1. 查看页面顶部的统计卡片
2. 点击想要查看的日志类型标签
3. 页面会只显示该类型的日志
4. 点击"显示全部"可以清除过滤

### 4. 查看详情
1. 点击任意日志条目
2. 弹出详情对话框显示完整信息
3. 可以在详情对话框中复制日志

### 5. 复制日志
- **复制单条**：点击日志条目右侧的复制按钮
- **复制全部**：点击顶部工具栏的复制按钮
- **复制过滤后的**：激活过滤后，复制按钮只复制当前显示的日志

### 6. 清除日志
1. 点击顶部工具栏的清除按钮
2. 确认清除操作
3. 所有API调用日志将被清除

## 调试信息内容

API调用日志包含以下类型的调试信息：

### 1. API请求信息
- 请求方法（GET、POST等）
- 请求URL
- 请求头信息
- 请求体数据
- 请求时间

### 2. API响应信息
- 响应URL
- 响应状态码
- 响应数据
- 错误信息（如果有）
- 响应时间

### 3. 登录相关
- 登录接口调用
- 认证头信息
- Token状态
- 登录结果

### 4. 网络状态
- 网络连接状态
- 网络类型
- 连接测试结果

## 后端开发支持

这个API调用日志功能对后端开发人员特别有用：

### 1. 接口调试
- 查看前端发送了哪些参数
- 确认请求格式是否正确
- 检查请求头和认证信息

### 2. 响应验证
- 查看后端返回的数据格式
- 确认响应状态码
- 检查错误信息

### 3. 问题排查
- 快速定位接口调用问题
- 分析请求和响应的时序
- 复制日志信息进行进一步分析

### 4. 接口测试
- 验证接口的正确性
- 测试不同参数的响应
- 检查错误处理逻辑

## 注意事项

1. **性能考虑**：日志服务有最大条数限制（1000条），超出后会自动清理旧日志
2. **隐私保护**：在生产环境中应该注意敏感信息的记录和显示
3. **存储空间**：日志会保存在本地存储中，定期清理可以释放空间
4. **调试模式**：某些详细的调试信息只在开发模式下显示

## 总结

新的API调用日志功能提供了一个强大而用户友好的工具，帮助开发人员和后端工程师更好地理解和调试API调用。该功能完全集成到现有的应用架构中，支持多语言和主题切换，与错误日志页面保持一致的用户体验。
