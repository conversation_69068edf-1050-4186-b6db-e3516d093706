# iOS权限修复指南

## 🚨 当前问题

从应用日志可以看到以下权限问题：
- ❌ **相机权限被拒绝** - `相机权限被拒绝，请在设置中授予相机权限`
- ❌ **蓝牙权限被拒绝** - `蓝牙权限被拒绝，请在设置中授予蓝牙权限`
- ✅ **定位权限正常** - `location.getCurrentPosition 执行成功`
- ✅ **系统功能正常** - 提示框、震动、分享等都正常工作

## 📱 iOS权限设置步骤

### 1. 相机权限设置

#### 方法一：通过应用设置
1. 打开iPhone **设置**
2. 滚动找到并点击 **Flutter Hybrid App**（或应用名称）
3. 找到 **相机** 选项
4. 将开关打开（绿色状态）

#### 方法二：通过隐私设置
1. 打开iPhone **设置**
2. 点击 **隐私与安全性**
3. 点击 **相机**
4. 找到 **Flutter Hybrid App** 并将开关打开

### 2. 相册权限设置

#### 通过隐私设置
1. 打开iPhone **设置**
2. 点击 **隐私与安全性**
3. 点击 **照片**
4. 找到 **Flutter Hybrid App**
5. 选择权限级别：
   - **所有照片** - 推荐，完全访问
   - **选定的照片** - 部分访问
   - **无** - 拒绝访问

### 3. 蓝牙权限设置

#### 通过隐私设置
1. 打开iPhone **设置**
2. 点击 **隐私与安全性**
3. 点击 **蓝牙**
4. 找到 **Flutter Hybrid App** 并将开关打开

#### 确保蓝牙已开启
1. 打开 **控制中心**（从右上角下滑）
2. 确保蓝牙图标是蓝色（已开启状态）
3. 或者到 **设置 > 蓝牙** 中开启

### 4. 定位权限设置（已正常）

定位权限当前工作正常，如需调整：
1. 打开iPhone **设置**
2. 点击 **隐私与安全性**
3. 点击 **定位服务**
4. 确保 **定位服务** 总开关已开启
5. 找到 **Flutter Hybrid App**
6. 选择权限级别：
   - **使用App时** - 推荐
   - **始终** - 如需后台定位
   - **永不** - 拒绝访问

## 🔧 权限设置后的操作

### 1. 重启应用
设置权限后，请完全关闭并重新打开应用：
1. 双击Home键（或从底部上滑并停留）
2. 找到应用并向上滑动关闭
3. 重新打开应用

### 2. 测试权限
使用应用中的权限测试工具：
1. 打开应用，进入"应用"页面
2. 找到"权限调试"应用
3. 逐一测试各项功能
4. 查看详细的权限状态

## 🎯 权限测试流程

### 使用权限调试工具
1. **打开权限调试页面**
   - 应用 > 设备功能测试 > 权限调试

2. **测试相机权限**
   - 点击"调试相机权限"
   - 如果成功，会显示拍照结果
   - 如果失败，会显示具体的错误信息和解决方案

3. **测试蓝牙权限**
   - 点击"调试蓝牙权限"
   - 如果成功，会显示扫描到的蓝牙设备
   - 如果失败，会显示权限错误信息

4. **测试相册权限**
   - 点击"调试相册权限"
   - 如果成功，会显示选择的图片信息
   - 如果失败，会显示权限错误信息

## ⚠️ 常见问题解决

### 问题1：设置中找不到应用
**解决方案：**
- 确保应用已经尝试过使用相应功能
- 重新安装应用
- 重启设备

### 问题2：权限开启后仍然失败
**解决方案：**
- 完全关闭应用并重新打开
- 重启设备
- 检查iOS系统版本是否支持

### 问题3：蓝牙权限设置后仍然失败
**解决方案：**
- 确保蓝牙功能已开启
- 检查是否有其他应用占用蓝牙
- 重启蓝牙功能（关闭再开启）

### 问题4：相机权限在设置中显示为灰色
**解决方案：**
- 检查是否开启了"屏幕时间"限制
- 到 设置 > 屏幕时间 > 内容和隐私访问限制 中检查
- 确保相机访问未被限制

## 📊 权限状态检查

### 当前权限状态（基于日志）
- ✅ **定位权限** - 正常工作
- ✅ **系统功能** - 提示框、震动、分享正常
- ✅ **NFC可用性** - 检查正常
- ❌ **相机权限** - 需要授权
- ❌ **相册权限** - 需要授权
- ❌ **蓝牙权限** - 需要授权

### 预期结果
权限设置完成后，所有功能应该正常工作：
- 📷 相机拍照和录像
- 🖼️ 相册图片选择
- 🔵 蓝牙设备扫描和连接
- 📍 位置获取（已正常）
- ⚙️ 系统功能（已正常）

## 🎉 验证成功

权限设置完成后，请使用以下方式验证：

1. **使用权限测试应用**
   - 所有测试按钮应该显示绿色（已授权）
   - 功能测试应该返回成功结果

2. **使用真实功能测试应用**
   - 相机功能应该能够正常拍照
   - 蓝牙功能应该能够扫描到设备
   - 相册功能应该能够选择图片

3. **检查应用日志**
   - 不应该再出现"权限被拒绝"的错误
   - 功能调用应该显示"执行成功"

---

## 📞 需要帮助？

如果按照以上步骤操作后仍然有问题，请：
1. 截图权限设置页面
2. 使用权限调试工具获取详细错误信息
3. 提供具体的错误日志

现在请按照上述步骤设置权限，然后重新测试应用功能！
