# 全接口模拟/真实切换功能实现说明

## 修改概述

根据用户需求，对整个应用的所有API接口进行了全面改造：

1. **移除持久化测试功能** - 删除了调试模式下的"测试登录持久化"按钮
2. **新增全局模拟登录选项** - 添加了一个勾选框，默认勾选，用于控制所有接口的调用方式
3. **支持所有接口真实调用** - 当取消勾选时，所有API都使用真实的后端接口
4. **动态服务器配置** - 真实接口的baseUrl基于当前选中的服务器配置
5. **全局设置持久化** - 模拟/真实接口的选择会保存到本地，影响所有API调用

## 具体修改内容

### 1. ApiService 全面改造 (`lib/services/api_service.dart`)

#### 新增功能：
- **全局模拟数据设置**：`getUseMockData()` 和 `setUseMockData()` 方法管理全局设置
- **动态baseUrl获取**：`getBaseUrl()` 方法根据当前服务器配置动态构建API地址
- **认证头管理**：`_getAuthHeaders()` 方法自动添加Bearer Token
- **所有接口支持双模式**：每个API方法都支持模拟和真实接口调用

#### 改造的接口列表：
1. **认证接口**
   - `login()` - 用户登录

2. **业务数据接口**
   - `getHomeInfo()` - 首页信息
   - `getAppList()` - 应用列表
   - `getMessages()` - 消息列表
   - `getWorkspaceApps()` - 工作台应用

3. **操作接口**
   - `saveFavoriteApps()` - 保存常用应用
   - `saveAppSort()` - 保存应用排序
   - `testConnection()` - 测试服务器连接

#### 核心代码结构：
```dart
// 全局设置管理
static Future<bool> getUseMockData() async {
  final prefs = await SharedPreferences.getInstance();
  return prefs.getBool(_useMockDataKey) ?? true;
}

static Future<void> setUseMockData(bool useMockData) async {
  final prefs = await SharedPreferences.getInstance();
  await prefs.setBool(_useMockDataKey, useMockData);
}

// 统一的接口调用模式
static Future<Map<String, dynamic>> someApi({
  bool? useMockData,
}) async {
  final shouldUseMockData = useMockData ?? await getUseMockData();

  if (shouldUseMockData) {
    // 模拟数据逻辑
    return mockResponse;
  } else {
    // 真实接口逻辑
    final baseUrl = await getBaseUrl();
    final response = await _httpClient.get('$baseUrl/endpoint');
    return processRealResponse(response);
  }
}
```

### 2. 登录页面修改 (`lib/screens/login_screen.dart`)

#### 新增功能：
- **全局模拟登录勾选框**：默认勾选，控制所有接口的调用方式
- **设置持久化**：勾选状态会保存到全局设置，影响整个应用
- **移除持久化测试**：删除了调试模式下的测试按钮

#### UI变更：
```dart
// 新增状态变量
bool _useMockLogin = true; // 默认勾选模拟登录

// 初始化时加载全局设置
Future<void> _loadMockLoginSetting() async {
  final useMockData = await ApiService.getUseMockData();
  setState(() {
    _useMockLogin = useMockData;
  });
}

// 新增模拟登录勾选框UI，支持全局设置
Row(
  children: [
    Checkbox(
      value: _useMockLogin,
      onChanged: (value) async {
        final newValue = value ?? true;
        setState(() {
          _useMockLogin = newValue;
        });
        // 同时更新全局设置
        await ApiService.setUseMockData(newValue);
      },
    ),
    Expanded(
      child: Text(
        LocalizationService.t('use_mock_login'),
        style: const TextStyle(fontSize: AppTheme.fontSizeSmall),
      ),
    ),
  ],
),

// 登录方法调用时传递参数
final result = await ApiService.login(
  _usernameController.text,
  _passwordController.text,
  useMockData: _useMockLogin,
);
```

### 3. 本地化文本添加 (`lib/services/localization_service.dart`)

#### 新增翻译：
- **中文**：`'use_mock_login': '使用模拟登录（勾选则使用模拟数据，取消勾选则使用真实接口）'`
- **英文**：`'use_mock_login': 'Use Mock Login (Check to use mock data, uncheck to use real API)'`

## 功能说明

### 模拟数据模式（默认）
- **全局影响**：勾选状态影响所有API接口调用
- **数据来源**：使用预设的模拟数据
- **登录凭据**：用户名 `admin`，密码 `123456`
- **响应速度**：快速响应，模拟网络延迟
- **适用场景**：开发测试、演示、无后端环境、离线开发

### 真实接口模式
- **全局影响**：取消勾选后所有API都调用真实后端
- **API地址**：基于服务器配置动态构建，格式为 `http://{服务器地址}:{端口}/api/{endpoint}`
- **认证机制**：自动添加Bearer Token到请求头
- **错误处理**：完整的HTTP错误处理和用户提示
- **适用场景**：生产环境、集成测试、真实业务场景

### 支持的API接口
1. **认证相关**
   - `/auth/login` - 用户登录

2. **数据获取**
   - `/home/<USER>
   - `/apps/list` - 应用列表（分类、应用详情）
   - `/messages/list` - 消息列表（系统、工作消息）
   - `/workspace/apps` - 工作台常用应用

3. **数据操作**
   - `/apps/favorites` - 保存常用应用设置
   - `/apps/sort` - 保存应用排序
   - `/system/ping` - 服务器连接测试

### 服务器配置集成
- **配置来源**：从SharedPreferences读取当前选中的服务器配置
- **默认配置**：localhost:8080
- **配置修改**：通过服务器配置页面可以修改服务器地址和端口
- **动态更新**：每次真实接口调用时都会重新获取最新的服务器配置
- **全局生效**：服务器配置对所有真实接口调用生效

## 使用方法

### 切换模式
1. **启用模拟数据模式**（推荐用于开发）：
   - 保持"使用模拟登录"勾选状态
   - 所有API调用都使用预设的模拟数据
   - 无需后端服务器，可离线使用

2. **启用真实接口模式**（用于生产）：
   - 取消勾选"使用模拟登录"
   - 确保服务器配置正确（通过服务器配置页面设置）
   - 所有API调用都连接真实后端服务器

### 登录操作
1. **模拟登录**：
   - 用户名：`admin`，密码：`123456`
   - 立即返回模拟的用户信息

2. **真实登录**：
   - 输入真实的用户名和密码
   - 连接后端验证用户身份

### 服务器配置
1. **配置服务器**：
   - 点击登录页面的"服务器配置"链接
   - 设置正确的服务器地址和端口
   - 保存配置后返回登录页面

2. **测试连接**：
   - 在服务器配置页面可以测试连接
   - 验证服务器是否可访问

## 技术实现细节

### 错误处理
- **网络错误**：使用HttpClient的错误处理机制
- **认证失败**：返回统一的错误格式
- **服务器错误**：显示HTTP状态码和错误信息

### 数据格式
- **请求格式**：JSON格式，包含username和password字段
- **响应格式**：统一的API响应格式，包含success、message和data字段
- **Token处理**：支持JWT token的存储和管理

### 兼容性
- **向后兼容**：保持原有的模拟登录功能不变
- **配置兼容**：支持现有的服务器配置系统
- **主题兼容**：支持深色/浅色主题切换
- **多语言兼容**：支持中文/英文界面

## 注意事项

1. **默认行为**：为了保持向后兼容，默认使用模拟数据模式
2. **全局影响**：模拟/真实模式的切换影响所有API接口调用
3. **设置持久化**：选择的模式会保存到本地，下次启动时自动恢复
4. **网络要求**：真实接口模式需要网络连接和正确的服务器配置
5. **API规范**：后端API需要遵循文档中定义的接口规范
6. **认证管理**：真实接口模式会自动管理Token认证
7. **安全考虑**：生产环境建议使用HTTPS协议
8. **错误处理**：完整的网络错误处理和用户友好的错误提示
9. **缓存策略**：两种模式都支持数据缓存，提升用户体验

## 开发建议

1. **开发阶段**：使用模拟数据模式，快速开发和测试UI功能
2. **集成测试**：切换到真实接口模式，验证与后端的集成
3. **生产部署**：确保真实接口模式下所有功能正常工作
4. **调试技巧**：可以随时切换模式来对比数据和排查问题
5. **后端开发**：参考`docs/backend_api_specification.md`实现对应的API接口
