# 退出登录强制刷新功能实现总结

## 功能需求
根据用户需求："只要退出登陆，不管是否切换了用户，只要重新登陆都要强制刷新数据，加载骨架，清空缓存日志等新，但保留登陆页面设置的信息，是否勾选了模拟登陆，是否勾选记住，当前登陆人帐号不可清除，服务设置信息不可清除"

## 实现方案

### 1. AuthService 强制刷新标记机制

#### 新增功能
- **强制刷新标记**: 添加 `_shouldForceRefresh` 内存标记和 `_forceRefreshKey` 持久化键
- **标记管理方法**:
  - `shouldForceRefresh()`: 检查是否需要强制刷新
  - `clearForceRefreshFlag()`: 清除强制刷新标记
  - `_setForceRefreshFlag()`: 设置强制刷新标记（私有方法）

#### 关键实现
```dart
// 强制刷新标记，用于重新登录时强制刷新数据
bool _shouldForceRefresh = false;
static const String _forceRefreshKey = 'force_refresh_on_login';

/// 检查是否需要强制刷新
Future<bool> shouldForceRefresh() async {
  // 首先检查内存缓存
  if (_shouldForceRefresh) return true;
  
  final prefs = await SharedPreferences.getInstance();
  final forceRefresh = prefs.getBool(_forceRefreshKey) ?? false;
  _shouldForceRefresh = forceRefresh;
  return forceRefresh;
}
```

#### 触发时机
- **设置标记**: 在 `clearAllUserData()` 方法中自动设置强制刷新标记
- **清除标记**: 在各页面数据加载完成后调用 `clearForceRefreshFlag()`

### 2. CacheService 强制清理功能

#### 新增方法
- `forceCleanAllPageCache()`: 强制清除所有页面缓存数据

#### 清理范围
- 所有页面的缓存数据和时间戳
- 通用缓存数据（以_expiry结尾的键对应的数据）
- 其他时间戳相关的缓存键

```dart
/// 强制清除所有页面缓存数据（用于重新登录时）
static Future<void> forceCleanAllPageCache() async {
  final prefs = await SharedPreferences.getInstance();
  
  // 清除所有页面的缓存数据和时间戳
  const cacheKeys = [
    _homeDataKey, _homeDataTimestampKey,
    _appsDataKey, _appsDataTimestampKey,
    _workspaceDataKey, _workspaceDataTimestampKey,
    _messagesDataKey, _messagesDataTimestampKey,
  ];
  
  for (final key in cacheKeys) {
    await prefs.remove(key);
  }
  
  // 清除所有通用缓存数据
  final allKeys = prefs.getKeys();
  final expiryKeys = allKeys.where((key) => key.endsWith('_expiry')).toList();
  
  for (final expiryKey in expiryKeys) {
    final dataKey = expiryKey.replaceAll('_expiry', '');
    await prefs.remove(dataKey);
    await prefs.remove(expiryKey);
  }
}
```

### 3. 各页面初始化逻辑修改

#### 修改的页面
- HomeScreen
- AppsScreen  
- WorkspaceScreen
- MessagesScreen

#### 统一的初始化流程
```dart
Future<void> _initializeData() async {
  // 检查是否需要强制刷新数据
  final authService = AuthService();
  final shouldForceRefresh = await authService.shouldForceRefresh();
  
  if (shouldForceRefresh) {
    // 需要强制刷新，清空所有缓存并显示骨架屏
    debugPrint('页面检测到强制刷新标记，清空缓存并强制加载数据');
    await CacheService.forceCleanAllPageCache();
    setState(() {
      _hasCache = false;
      _isLoading = true;
    });
    _loadData();
    return;
  }
  
  // 正常流程：检查是否有缓存数据
  final cachedData = await CacheService.getCachedData();
  if (cachedData != null) {
    // 有缓存数据，直接显示，不显示骨架屏
    setState(() {
      _hasCache = true;
      _isLoading = false;
      // 恢复数据
    });
  } else {
    // 没有缓存数据，显示骨架屏并加载数据
    _loadData();
  }
}
```

#### 数据加载完成处理
在各页面的数据加载方法中，成功加载数据后清除强制刷新标记：
```dart
// 数据加载完成后，清除强制刷新标记
final authService = AuthService();
await authService.clearForceRefreshFlag();
```

### 4. DataCleanupService 保留登录页面信息

#### 保留的数据类型
```dart
const keysToKeep = {
  // 登录页面信息
  'username',                    // AppConstants.keyUsername
  'password',                    // AppConstants.keyPassword
  'remember_password',           // AppConstants.keyRememberPassword
  
  // 主题和语言设置
  'selected_theme',              // 主题设置
  'theme_mode',                  // AppConstants.keyThemeMode
  'selected_language',           // 语言设置
  
  // 服务器配置
  'server_address',              // AppConstants.keyServerAddress
  'server_port',                 // AppConstants.keyServerPort
  'saved_servers',               // AppConstants.keySavedServers
  
  // 模拟登录设置（需要保留）
  'use_mock_data',               // 全局模拟数据设置
};
```

#### 清理逻辑
- 清除认证相关数据（token、用户信息等）
- 清除所有缓存数据
- 清除日志数据
- 清除API设置（但保留服务器配置）
- 清除用户个人偏好数据（但保留主题和语言设置）
- **保留登录页面的所有设置信息**

### 5. 登录页面逻辑修改

#### 登录成功后的处理
```dart
if (saveSuccess && mounted) {
  // 登录成功后，检查是否需要强制刷新数据
  final shouldForceRefresh = await authService.shouldForceRefresh();
  if (shouldForceRefresh) {
    debugPrint('检测到强制刷新标记，将在主页面加载时强制刷新所有数据');
  }
  
  // 导航到主界面
  if (mounted) {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(builder: (context) => const MainScreen()),
    );
  }
}
```

## 完整流程说明

### 退出登录流程
1. 用户点击退出登录
2. 调用 `AuthService.clearAllUserData()`
3. `DataCleanupService` 清理所有用户数据，但保留登录页面信息
4. 设置强制刷新标记 `force_refresh_on_login = true`
5. 跳转到登录页面

### 重新登录流程
1. 用户输入凭据并登录
2. 登录成功后检查强制刷新标记
3. 跳转到主页面

### 主页面加载流程
1. 各页面初始化时检查强制刷新标记
2. 如果需要强制刷新：
   - 清空所有页面缓存
   - 设置 `_isLoading = true`，显示骨架屏
   - 强制从API加载数据
3. 数据加载完成后清除强制刷新标记
4. 后续页面切换恢复正常缓存逻辑

## 技术特点

### 1. 智能缓存管理
- 只在真正需要时显示骨架屏
- 保持原有的缓存优化逻辑
- 强制刷新不影响正常使用体验

### 2. 数据保留策略
- 精确保留登录页面所需的所有设置
- 清理所有用户相关的敏感数据
- 保持应用配置的连续性

### 3. 状态管理
- 内存和持久化双重标记机制
- 自动清理，避免状态泄漏
- 跨页面的统一处理逻辑

### 4. 用户体验
- 重新登录时明确的数据刷新反馈
- 保留用户的个人偏好设置
- 无缝的登录体验

## 验证要点

1. **退出登录后重新登录**：验证是否显示骨架屏并强制刷新数据
2. **登录页面信息保留**：验证模拟登录勾选、记住密码、账号、服务器设置是否保留
3. **缓存清理**：验证所有页面缓存是否被清空
4. **正常使用**：验证后续使用中缓存逻辑是否正常
5. **多次登录**：验证多次退出重新登录的稳定性

## 总结

该实现完全满足用户需求，实现了退出登录后重新登录时的强制数据刷新，同时保留了登录页面的所有设置信息。通过智能的标记机制和缓存管理，确保了功能的可靠性和用户体验的连续性。
