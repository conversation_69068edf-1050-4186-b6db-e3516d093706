# WebView JavaScript API 完整文档

## 1. 概述

本文档详细描述了WebView中可用的所有JavaScript API，包括相机、二维码、蓝牙、NFC、定位、设备信息等功能。

## 2. 全局API对象

### 2.1 API结构

```javascript
window.NativeAPI = {
  Camera: { /* 相机功能 */ },
  QRCode: { /* 二维码功能 */ },
  Bluetooth: { /* 蓝牙功能 */ },
  NFC: { /* NFC功能 */ },
  Location: { /* 定位功能 */ },
  Device: { /* 设备信息 */ },
  TitleBar: { /* 标题栏控制 */ },
  System: { /* 系统功能 */ }
};
```

### 2.2 统一调用方式

```javascript
// 所有API都返回Promise
const result = await NativeAPI.Camera.takePhoto(options);

// 错误处理
try {
  const result = await NativeAPI.QRCode.scan();
  console.log('扫描成功:', result);
} catch (error) {
  console.error('扫描失败:', error.message);
}
```

## 3. 相机功能 API

### 3.1 拍照

```javascript
// 基础拍照
const result = await NativeAPI.Camera.takePhoto();

// 高级选项
const result = await NativeAPI.Camera.takePhoto({
  quality: 80,                    // 图片质量 (0-100)
  maxWidth: 1024,                 // 最大宽度
  maxHeight: 1024,                // 最大高度
  source: 'camera',               // 来源: camera, gallery
  preferredCameraDevice: 'rear'   // 摄像头: rear, front
});

// 返回结果
{
  success: true,
  data: {
    path: '/path/to/image.jpg',
    name: 'image.jpg',
    size: 1024000,
    width: 1024,
    height: 768,
    quality: 80,
    mimeType: 'image/jpeg',
    lastModified: '2024-01-01T12:00:00.000Z'
  }
}
```

### 3.2 录像

```javascript
const result = await NativeAPI.Camera.recordVideo({
  maxDuration: 60,                // 最大录制时长(秒)
  preferredCameraDevice: 'rear'   // 摄像头选择
});

// 返回结果
{
  success: true,
  data: {
    path: '/path/to/video.mp4',
    name: 'video.mp4',
    size: 5120000,
    duration: 30,
    mimeType: 'video/mp4'
  }
}
```

### 3.3 相册选择

```javascript
// 选择单个文件
const result = await NativeAPI.Camera.pickFromGallery({
  type: 'image',        // image, video, media
  quality: 80,
  maxWidth: 1024,
  maxHeight: 1024
});

// 选择多个文件
const result = await NativeAPI.Camera.pickMultipleFromGallery({
  maxCount: 5,          // 最大选择数量
  quality: 80
});

// 返回结果（多选）
{
  success: true,
  data: {
    files: [
      { path: '/path/1.jpg', name: '1.jpg', size: 1024000 },
      { path: '/path/2.jpg', name: '2.jpg', size: 2048000 }
    ],
    count: 2,
    maxCount: 5
  }
}
```

### 3.4 图片压缩

```javascript
const result = await NativeAPI.Camera.compressImage({
  path: '/path/to/image.jpg',
  quality: 60,
  maxWidth: 800,
  maxHeight: 600,
  keepExif: false
});

// 返回结果
{
  success: true,
  data: {
    originalPath: '/path/to/image.jpg',
    compressedPath: '/path/to/compressed.jpg',
    originalSize: 2048000,
    compressedSize: 512000,
    compressionRatio: 0.75
  }
}
```

## 4. 二维码功能 API

### 4.1 扫描二维码

```javascript
// 基础扫描
const result = await NativeAPI.QRCode.scan();

// 高级选项
const result = await NativeAPI.QRCode.scan({
  prompt: '请将二维码放入扫描框内',
  timeout: 30000,
  formats: ['QR_CODE', 'CODE_128', 'CODE_39']
});

// 返回结果
{
  success: true,
  data: {
    data: 'https://example.com',
    format: 'QR_CODE',
    type: 'URL',
    timestamp: '2024-01-01T12:00:00.000Z'
  }
}
```

### 4.2 生成二维码

```javascript
const result = await NativeAPI.QRCode.generate({
  data: 'Hello World',
  size: 200,
  errorCorrectionLevel: 'M',      // L, M, Q, H
  foregroundColor: '#000000',
  backgroundColor: '#FFFFFF'
});

// 返回结果
{
  success: true,
  data: {
    imagePath: '/path/to/qr.png',
    base64: 'data:image/png;base64,iVBORw0KGgo...',
    size: 200,
    data: 'Hello World',
    version: 1
  }
}
```

### 4.3 批量扫描

```javascript
const result = await NativeAPI.QRCode.scanMultiple({
  maxCount: 10,
  autoStop: false     // 遇到重复时是否自动停止
});

// 返回结果
{
  success: true,
  data: {
    results: [
      { data: 'QR1', format: 'QR_CODE', type: 'TEXT' },
      { data: 'QR2', format: 'QR_CODE', type: 'URL' }
    ],
    count: 2,
    maxCount: 10,
    completed: false
  }
}
```

## 5. 蓝牙功能 API

### 5.1 设备管理

```javascript
// 检查蓝牙状态
const status = await NativeAPI.Bluetooth.isEnabled();

// 启用蓝牙
const enabled = await NativeAPI.Bluetooth.enable();

// 扫描设备
const devices = await NativeAPI.Bluetooth.scanDevices({
  timeout: 10000,
  includeUnknownDevices: false
});

// 返回结果
{
  success: true,
  data: {
    devices: [
      {
        id: '00:11:22:33:44:55',
        name: 'Bluetooth Printer',
        address: '00:11:22:33:44:55',
        bonded: true,
        connected: false,
        rssi: -45
      }
    ],
    count: 1
  }
}

// 连接设备
const connected = await NativeAPI.Bluetooth.connect({
  deviceId: '00:11:22:33:44:55',
  timeout: 10000
});

// 断开连接
await NativeAPI.Bluetooth.disconnect();

// 获取连接状态
const status = await NativeAPI.Bluetooth.getConnectionStatus();
```

### 5.2 打印功能

```javascript
// 打印文本
await NativeAPI.Bluetooth.printText({
  text: 'Hello World',
  fontSize: 'large',      // normal, large, small
  align: 'center',        // left, center, right
  bold: true,
  underline: false
});

// 打印图片
await NativeAPI.Bluetooth.printImage({
  imagePath: '/path/to/image.jpg',
  width: 384,
  align: 'center'
});

// 打印二维码
await NativeAPI.Bluetooth.printQRCode({
  data: 'https://example.com',
  size: 200,
  align: 'center',
  errorLevel: 'M'
});

// 打印小票
await NativeAPI.Bluetooth.printReceipt({
  title: '购物小票',
  items: [
    { name: '苹果', price: '5.00', qty: '2' },
    { name: '香蕉', price: '3.00', qty: '1' }
  ],
  total: '13.00',
  footer: '感谢您的光临！'
});

// 切纸
await NativeAPI.Bluetooth.cutPaper();
```

## 6. NFC功能 API

### 6.1 基础功能

```javascript
// 检查NFC可用性
const available = await NativeAPI.NFC.isAvailable();

// 开始扫描
const result = await NativeAPI.NFC.startScan({
  timeout: 30000
});

// 停止扫描
await NativeAPI.NFC.stopScan();

// 返回结果
{
  success: true,
  data: {
    id: 'tag_id_123',
    type: 'NDEF',
    technologies: ['Ndef', 'NfcA'],
    data: {
      ndef: [
        {
          typeNameFormat: 1,
          type: 'T',
          payload: 'Hello NFC'
        }
      ]
    }
  }
}
```

### 6.2 标签操作

```javascript
// 写入文本
await NativeAPI.NFC.writeTag({
  type: 'text',
  data: 'Hello NFC!'
});

// 写入URL
await NativeAPI.NFC.writeTag({
  type: 'url',
  data: 'https://example.com'
});

// 写入WiFi信息
await NativeAPI.NFC.writeTag({
  type: 'wifi',
  data: {
    ssid: 'MyWiFi',
    password: 'password123',
    security: 'WPA2'
  }
});
```

## 7. 定位功能 API

### 7.1 获取位置

```javascript
// 获取当前位置
const position = await NativeAPI.Location.getCurrentPosition({
  accuracy: 'high',       // high, medium, low
  timeout: 15000,
  maximumAge: 60000
});

// 返回结果
{
  success: true,
  data: {
    latitude: 39.9042,
    longitude: 116.4074,
    accuracy: 10,
    altitude: 50,
    heading: 90,
    speed: 0,
    timestamp: '2024-01-01T12:00:00.000Z'
  }
}

// 监听位置变化
const watchId = await NativeAPI.Location.watchPosition({
  callback: (position) => {
    console.log('位置更新:', position);
  },
  accuracy: 'high',
  distanceFilter: 10
});

// 停止监听
await NativeAPI.Location.clearWatch(watchId);
```

### 7.2 地址解析

```javascript
// 地址转坐标
const coordinates = await NativeAPI.Location.geocode({
  address: '北京市朝阳区'
});

// 坐标转地址
const address = await NativeAPI.Location.reverseGeocode({
  latitude: 39.9042,
  longitude: 116.4074
});

// 返回结果
{
  success: true,
  data: {
    address: '北京市朝阳区某某街道',
    country: '中国',
    province: '北京市',
    city: '北京市',
    district: '朝阳区',
    street: '某某街道'
  }
}
```

## 8. 设备信息 API

```javascript
// 获取设备唯一标识
const deviceId = await NativeAPI.Device.getDeviceId();

// 获取设备信息
const deviceInfo = await NativeAPI.Device.getDeviceInfo();

// 返回结果
{
  success: true,
  data: {
    deviceId: 'unique_device_id_123',
    platform: 'android',
    model: 'SM-G973F',
    manufacturer: 'Samsung',
    osVersion: '11',
    appVersion: '1.0.0',
    buildNumber: '100'
  }
}

// 获取用户信息
const userInfo = await NativeAPI.Device.getUserInfo();

// 获取应用信息
const appInfo = await NativeAPI.Device.getAppInfo();
```

## 9. 标题栏控制 API

```javascript
// 设置标题
await NativeAPI.TitleBar.setTitle('新标题');

// 设置右侧按钮
await NativeAPI.TitleBar.setRightButton({
  text: '历史',
  icon: 'history',
  action: 'history'
});

// 隐藏右侧按钮
await NativeAPI.TitleBar.hideRightButton();

// 控制返回按钮
await NativeAPI.TitleBar.showBackButton(true);
```

## 10. 系统功能 API

```javascript
// 显示提示
await NativeAPI.System.showToast('操作成功', 2000);

// 显示警告
await NativeAPI.System.showAlert('提示', '这是一个警告消息');

// 显示确认对话框
const confirmed = await NativeAPI.System.showConfirm('确认', '确定要删除吗？');

// 震动
await NativeAPI.System.vibrate([100, 200, 100]);

// 打开URL
await NativeAPI.System.openUrl('https://example.com');

// 分享内容
await NativeAPI.System.shareContent({
  text: '分享的文本',
  url: 'https://example.com',
  title: '分享标题'
});
```

## 11. 错误处理

### 11.1 错误类型

```javascript
const ErrorCodes = {
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  FEATURE_NOT_SUPPORTED: 'FEATURE_NOT_SUPPORTED',
  TIMEOUT: 'TIMEOUT',
  OPERATION_CANCELLED: 'OPERATION_CANCELLED',
  // ... 更多错误码
};
```

### 11.2 统一错误处理

```javascript
try {
  const result = await NativeAPI.Camera.takePhoto();
} catch (error) {
  switch (error.code) {
    case 'PERMISSION_DENIED':
      alert('请允许访问相机权限');
      break;
    case 'CAMERA_NOT_AVAILABLE':
      alert('相机不可用');
      break;
    default:
      alert(`操作失败: ${error.message}`);
  }
}
```

## 12. 最佳实践

### 12.1 权限检查

```javascript
// 在使用功能前检查权限
async function checkCameraPermission() {
  try {
    await NativeAPI.Camera.takePhoto();
    return true;
  } catch (error) {
    if (error.code === 'PERMISSION_DENIED') {
      // 引导用户授权
      return false;
    }
    throw error;
  }
}
```

### 12.2 功能检测

```javascript
// 检查功能是否可用
function isFeatureSupported(feature) {
  return typeof NativeAPI[feature] !== 'undefined';
}

if (isFeatureSupported('NFC')) {
  // 使用NFC功能
}
```

### 12.3 异步操作管理

```javascript
// 使用loading状态
async function scanQRCode() {
  showLoading(true);
  try {
    const result = await NativeAPI.QRCode.scan();
    handleScanResult(result);
  } catch (error) {
    handleError(error);
  } finally {
    showLoading(false);
  }
}
```
