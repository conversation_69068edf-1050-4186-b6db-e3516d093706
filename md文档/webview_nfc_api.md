# WebView NFC 功能 API 文档

## 功能概述
提供 WebView 中使用 NFC (Near Field Communication) 功能的完整解决方案，支持 NFC 标签读取、写入、检测等功能。

## JavaScript API

### 基础功能
```javascript
// 检查 NFC 是否可用
const available = await NFCAPI.isAvailable();

// 开始 NFC 扫描
const result = await NFCAPI.startScan({
    timeout: 30000        // 扫描超时时间(毫秒)
});

// 停止 NFC 扫描
await NFCAPI.stopScan();
```

### 标签操作
```javascript
// 读取 NFC 标签
const tagData = await NFCAPI.readTag();

// 写入 NFC 标签
await NFCAPI.writeTag({
    type: 'text',         // text, url, wifi, contact
    data: 'Hello NFC!'
});

// 写入 URL 到标签
await NFCAPI.writeTag({
    type: 'url',
    data: 'https://example.com'
});

// 写入 WiFi 信息到标签
await NFCAPI.writeTag({
    type: 'wifi',
    data: {
        ssid: 'MyWiFi',
        password: 'password123',
        security: 'WPA'     // OPEN, WEP, WPA, WPA2
    }
});
```

## Flutter 实现

### 依赖配置
```yaml
dependencies:
  nfc_manager: ^3.3.0
  permission_handler: ^11.3.1
```

### 权限配置
```xml
<!-- Android -->
<uses-permission android:name="android.permission.NFC" />
<uses-feature 
    android:name="android.hardware.nfc" 
    android:required="false" />

<!-- iOS -->
<!-- Info.plist -->
<key>com.apple.developer.nfc.readersession.formats</key>
<array>
    <string>NDEF</string>
    <string>TAG</string>
</array>
```

### 核心实现
```dart
class NFCPlugin implements NativePlugin {
  @override
  String get name => 'nfc';
  
  @override
  Future<dynamic> execute(String method, Map<String, dynamic> params) async {
    switch (method) {
      case 'isAvailable':
        return await _isAvailable();
      case 'startScan':
        return await _startScan(params);
      case 'stopScan':
        return await _stopScan();
      case 'readTag':
        return await _readTag();
      case 'writeTag':
        return await _writeTag(params);
    }
  }
  
  Future<bool> _isAvailable() async {
    return await NfcManager.instance.isAvailable();
  }
  
  Future<Map<String, dynamic>> _startScan(Map<String, dynamic> params) async {
    final timeout = params['timeout'] as int? ?? 30000;
    
    Completer<Map<String, dynamic>> completer = Completer();
    
    NfcManager.instance.startSession(
      onDiscovered: (NfcTag tag) async {
        final tagData = await _parseNfcTag(tag);
        completer.complete(tagData);
      },
    );
    
    // 设置超时
    Timer(Duration(milliseconds: timeout), () {
      if (!completer.isCompleted) {
        NfcManager.instance.stopSession();
        completer.completeError('NFC 扫描超时');
      }
    });
    
    return completer.future;
  }
  
  Future<Map<String, dynamic>> _parseNfcTag(NfcTag tag) async {
    final tagData = {
      'id': _getTagId(tag),
      'type': _getTagType(tag),
      'technologies': tag.data.keys.toList(),
      'data': {},
    };
    
    // 解析 NDEF 数据
    if (tag.data.containsKey('ndef')) {
      final ndef = Ndef.from(tag);
      if (ndef != null) {
        final ndefMessage = await ndef.read();
        if (ndefMessage != null) {
          tagData['data']['ndef'] = _parseNdefMessage(ndefMessage);
        }
      }
    }
    
    return tagData;
  }
  
  List<Map<String, dynamic>> _parseNdefMessage(NdefMessage message) {
    return message.records.map((record) {
      return {
        'typeNameFormat': record.typeNameFormat.index,
        'type': String.fromCharCodes(record.type),
        'identifier': String.fromCharCodes(record.identifier),
        'payload': String.fromCharCodes(record.payload),
      };
    }).toList();
  }
  
  Future<bool> _writeTag(Map<String, dynamic> params) async {
    final type = params['type'] as String;
    final data = params['data'];
    
    Completer<bool> completer = Completer();
    
    NfcManager.instance.startSession(
      onDiscovered: (NfcTag tag) async {
        try {
          final ndef = Ndef.from(tag);
          if (ndef == null || !ndef.isWritable) {
            completer.completeError('标签不支持写入');
            return;
          }
          
          final ndefMessage = _createNdefMessage(type, data);
          await ndef.write(ndefMessage);
          
          completer.complete(true);
        } catch (e) {
          completer.completeError('写入失败: ${e.toString()}');
        }
      },
    );
    
    return completer.future;
  }
  
  NdefMessage _createNdefMessage(String type, dynamic data) {
    NdefRecord record;
    
    switch (type) {
      case 'text':
        record = NdefRecord.createText(data as String);
        break;
      case 'url':
        record = NdefRecord.createUri(Uri.parse(data as String));
        break;
      case 'wifi':
        final wifiData = data as Map<String, dynamic>;
        record = NdefRecord.createWifiNetwork(
          ssid: wifiData['ssid'],
          password: wifiData['password'],
          networkType: _getWifiNetworkType(wifiData['security']),
        );
        break;
      default:
        record = NdefRecord.createText(data.toString());
    }
    
    return NdefMessage([record]);
  }
  
  WifiNetworkType _getWifiNetworkType(String security) {
    switch (security?.toUpperCase()) {
      case 'OPEN':
        return WifiNetworkType.open;
      case 'WEP':
        return WifiNetworkType.wep;
      case 'WPA':
        return WifiNetworkType.wpa;
      case 'WPA2':
        return WifiNetworkType.wpa2;
      default:
        return WifiNetworkType.wpa2;
    }
  }
}
```

## 使用示例

### NFC 标签读取器
```html
<div id="nfc-reader">
    <h3>NFC 标签读取器</h3>
    <button onclick="checkNFCAvailability()">检查 NFC 可用性</button>
    <button onclick="startNFCScan()">开始扫描</button>
    <button onclick="stopNFCScan()">停止扫描</button>
    <div id="nfc-result"></div>
</div>

<script>
async function checkNFCAvailability() {
    try {
        const available = await NFCAPI.isAvailable();
        updateResult(available ? 'NFC 功能可用' : 'NFC 功能不可用');
    } catch (error) {
        updateResult('检查 NFC 失败: ' + error.message);
    }
}

async function startNFCScan() {
    try {
        updateResult('正在扫描 NFC 标签，请将标签靠近设备...');
        
        const result = await NFCAPI.startScan({
            timeout: 30000
        });
        
        updateResult('扫描成功:\n' + JSON.stringify(result, null, 2));
        
        // 处理不同类型的标签数据
        handleNFCResult(result);
        
    } catch (error) {
        updateResult('NFC 扫描失败: ' + error.message);
    }
}

async function stopNFCScan() {
    try {
        await NFCAPI.stopScan();
        updateResult('已停止 NFC 扫描');
    } catch (error) {
        updateResult('停止扫描失败: ' + error.message);
    }
}

function handleNFCResult(result) {
    if (result.data && result.data.ndef) {
        const records = result.data.ndef;
        
        records.forEach(record => {
            const payload = record.payload;
            
            // 检测 URL
            if (payload.startsWith('http')) {
                if (confirm(`检测到网址，是否打开？\n${payload}`)) {
                    window.open(payload, '_blank');
                }
            }
            // 检测文本
            else if (record.type === 'T') {
                alert(`NFC 文本内容: ${payload}`);
            }
        });
    }
}

function updateResult(message) {
    document.getElementById('nfc-result').innerHTML = 
        '<pre>' + message + '</pre>';
}
</script>
```

### NFC 标签写入器
```html
<div id="nfc-writer">
    <h3>NFC 标签写入器</h3>
    
    <div>
        <label>写入类型:</label>
        <select id="write-type">
            <option value="text">文本</option>
            <option value="url">网址</option>
            <option value="wifi">WiFi</option>
        </select>
    </div>
    
    <div id="text-input" class="input-group">
        <label>文本内容:</label>
        <input type="text" id="text-data" placeholder="输入要写入的文本">
    </div>
    
    <div id="url-input" class="input-group" style="display:none;">
        <label>网址:</label>
        <input type="url" id="url-data" placeholder="https://example.com">
    </div>
    
    <div id="wifi-input" class="input-group" style="display:none;">
        <label>WiFi 名称:</label>
        <input type="text" id="wifi-ssid" placeholder="WiFi 名称">
        <label>WiFi 密码:</label>
        <input type="password" id="wifi-password" placeholder="WiFi 密码">
        <label>安全类型:</label>
        <select id="wifi-security">
            <option value="WPA2">WPA2</option>
            <option value="WPA">WPA</option>
            <option value="WEP">WEP</option>
            <option value="OPEN">开放</option>
        </select>
    </div>
    
    <button onclick="writeNFCTag()">写入标签</button>
    <div id="write-result"></div>
</div>

<script>
// 切换输入界面
document.getElementById('write-type').addEventListener('change', function() {
    const type = this.value;
    document.querySelectorAll('.input-group').forEach(group => {
        group.style.display = 'none';
    });
    document.getElementById(type + '-input').style.display = 'block';
});

async function writeNFCTag() {
    try {
        const type = document.getElementById('write-type').value;
        let data;
        
        switch (type) {
            case 'text':
                data = document.getElementById('text-data').value;
                if (!data) {
                    alert('请输入文本内容');
                    return;
                }
                break;
                
            case 'url':
                data = document.getElementById('url-data').value;
                if (!data) {
                    alert('请输入网址');
                    return;
                }
                break;
                
            case 'wifi':
                data = {
                    ssid: document.getElementById('wifi-ssid').value,
                    password: document.getElementById('wifi-password').value,
                    security: document.getElementById('wifi-security').value
                };
                if (!data.ssid) {
                    alert('请输入 WiFi 名称');
                    return;
                }
                break;
        }
        
        updateWriteResult('请将 NFC 标签靠近设备进行写入...');
        
        await NFCAPI.writeTag({
            type: type,
            data: data
        });
        
        updateWriteResult('写入成功！');
        
    } catch (error) {
        updateWriteResult('写入失败: ' + error.message);
    }
}

function updateWriteResult(message) {
    document.getElementById('write-result').textContent = message;
}
</script>
```

## 最佳实践

### 1. 权限处理
```javascript
// 检查并请求 NFC 权限
async function ensureNFCPermission() {
    const available = await NFCAPI.isAvailable();
    if (!available) {
        throw new Error('设备不支持 NFC 或 NFC 未启用');
    }
}
```

### 2. 错误处理
```javascript
try {
    await NFCAPI.startScan();
} catch (error) {
    switch (error.code) {
        case 'NFC_NOT_AVAILABLE':
            alert('NFC 功能不可用');
            break;
        case 'NFC_DISABLED':
            alert('请在设置中启用 NFC');
            break;
        case 'PERMISSION_DENIED':
            alert('需要 NFC 权限');
            break;
        default:
            alert('NFC 操作失败: ' + error.message);
    }
}
```

### 3. 用户体验优化
```javascript
// 显示扫描状态
function showScanningStatus() {
    const statusDiv = document.getElementById('scan-status');
    statusDiv.innerHTML = `
        <div class="scanning-animation">
            <div class="