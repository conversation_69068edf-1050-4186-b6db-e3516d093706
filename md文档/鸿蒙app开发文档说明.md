# 鸿蒙企业移动办公系统开发文档 v2.0

## 文档变更记录
| 版本 | 日期 | 变更内容 | 作者 |
|------|------|----------|------|
| v1.0 | 2024-12 | 初版文档创建 | 系统架构师 |
| v2.0 | 2024-12 | 补充详细设计和实现细节 | 高级工程师 |

---

## 目录
1. [项目概述](#1-项目概述)
2. [技术架构深度设计](#2-技术架构深度设计)
3. [详细功能规格说明](#3-详细功能规格说明)
4. [UI/UX设计系统](#4-uiux设计系统)
5. [组件库设计规范](#5-组件库设计规范)
6. [数据架构与API设计](#6-数据架构与api设计)
7. [状态管理深度设计](#7-状态管理深度设计)
8. [性能优化与监控](#8-性能优化与监控)
9. [安全架构设计](#9-安全架构设计)
10. [测试策略与质量保证](#10-测试策略与质量保证)
11. [国际化与本地化](#11-国际化与本地化)
12. [设备适配与兼容性](#12-设备适配与兼容性)
13. [开发工具链与环境](#13-开发工具链与环境)
14. [部署与运维](#14-部署与运维)
15. [项目管理与协作](#15-项目管理与协作)

---

## 1. 项目概述

### 1.1 项目背景与目标
基于现有Flutter企业移动办公系统的成功经验，开发一款功能完全对等的鸿蒙原生应用。该项目旨在为企业用户提供原生鸿蒙体验，充分利用鸿蒙系统的特性和优势。

**核心目标**:
- 100%功能对等性：确保所有Flutter版本功能在鸿蒙版本中完整实现
- 原生体验优化：充分利用鸿蒙系统特性，提供更好的用户体验
- 性能优势：相比跨平台方案，提供更好的性能表现
- 生态集成：深度集成鸿蒙生态系统功能

### 1.2 技术选型详细说明

#### 1.2.1 开发语言选择
**ArkTS (推荐)**:
- 基于TypeScript，学习成本低
- 鸿蒙官方推荐，生态支持完善
- 类型安全，开发效率高
- 与ArkUI框架深度集成

**备选方案**:
- JavaScript：快速开发，但类型安全性较差
- C++：性能最优，但开发复杂度高

#### 1.2.2 开发框架深度分析
**ArkUI框架优势**:
- 声明式UI开发，类似React/Flutter
- 组件化架构，代码复用性强
- 响应式数据绑定
- 丰富的内置组件库
- 优秀的性能表现

#### 1.2.3 开发工具链
**DevEco Studio特性**:
- 智能代码补全和错误检测
- 可视化UI设计器
- 实时预览功能
- 性能分析工具
- 设备模拟器集成

### 1.3 项目约束与假设

#### 1.3.1 技术约束
- 最低支持HarmonyOS 4.0 (API Level 10)
- 目标设备：手机、平板
- 网络要求：支持4G/5G/WiFi
- 存储要求：最小50MB可用空间

#### 1.3.2 业务约束
- 必须保持与Flutter版本的功能一致性
- 支持离线模式的基本功能
- 数据同步延迟不超过5秒
- 支持多租户架构

---

## 2. 技术架构深度设计

### 2.1 整体架构模式

#### 2.1.1 分层架构设计
```
┌─────────────────────────────────────┐
│           Presentation Layer        │  ← UI组件、页面、用户交互
├─────────────────────────────────────┤
│           Business Layer            │  ← 业务逻辑、状态管理、数据处理
├─────────────────────────────────────┤
│           Service Layer             │  ← 网络服务、本地服务、工具类
├─────────────────────────────────────┤
│           Data Layer                │  ← 数据存储、缓存、数据源
└─────────────────────────────────────┘
```

#### 2.1.2 模块化架构详细设计
```
entry/                                 # 主入口模块
├── src/main/ets/
│   ├── entryability/
│   │   ├── EntryAbility.ets           # 应用入口
│   │   └── AbilityStage.ets           # 应用生命周期管理
│   ├── pages/
│   │   ├── Index.ets                  # 主页面
│   │   ├── Login.ets                  # 登录页面
│   │   └── MainTabs.ets               # 主标签页
│   ├── common/
│   │   ├── constants/                 # 常量定义
│   │   ├── utils/                     # 工具类
│   │   ├── components/                # 通用组件
│   │   └── types/                     # 类型定义
│   └── viewmodel/
│       ├── AppViewModel.ets           # 应用级状态管理
│       └── UserViewModel.ets          # 用户状态管理

features/                              # 功能模块
├── auth/                              # 认证模块
│   ├── src/main/ets/
│   │   ├── pages/
│   │   │   ├── LoginPage.ets
│   │   │   └── ServerConfigPage.ets
│   │   ├── components/
│   │   │   ├── LoginForm.ets
│   │   │   └── ServerConfigForm.ets
│   │   ├── viewmodel/
│   │   │   └── AuthViewModel.ets
│   │   ├── model/
│   │   │   ├── User.ets
│   │   │   └── LoginRequest.ets
│   │   └── service/
│   │       └── AuthService.ets
│   └── oh-package.json5

├── home/                              # 首页模块
│   ├── src/main/ets/
│   │   ├── pages/
│   │   │   └── HomePage.ets
│   │   ├── components/
│   │   │   ├── BannerCarousel.ets
│   │   │   ├── NewsSection.ets
│   │   │   └── NewsItem.ets
│   │   ├── viewmodel/
│   │   │   └── HomeViewModel.ets
│   │   ├── model/
│   │   │   ├── Banner.ets
│   │   │   └── News.ets
│   │   └── service/
│   │       └── HomeService.ets
│   └── oh-package.json5

├── apps/                              # 应用中心模块
├── workspace/                         # 工作台模块
├── messages/                          # 消息模块
└── settings/                          # 设置模块

shared/                                # 共享模块
├── src/main/ets/
│   ├── network/
│   │   ├── HttpClient.ets             # HTTP客户端
│   │   ├── ApiEndpoints.ets           # API端点定义
│   │   └── NetworkInterceptor.ets     # 网络拦截器
│   ├── storage/
│   │   ├── PreferencesManager.ets     # 偏好设置管理
│   │   ├── DatabaseManager.ets        # 数据库管理
│   │   └── CacheManager.ets           # 缓存管理
│   ├── security/
│   │   ├── EncryptionUtil.ets         # 加密工具
│   │   └── TokenManager.ets           # Token管理
│   ├── themes/
│   │   ├── AppTheme.ets               # 应用主题
│   │   ├── Colors.ets                 # 颜色定义
│   │   └── Typography.ets             # 字体定义
│   └── components/
│       ├── base/                      # 基础组件
│       ├── business/                  # 业务组件
│       └── layout/                    # 布局组件
└── oh-package.json5
```

### 2.2 依赖注入架构

#### 2.2.1 服务定位器模式
```typescript
// ServiceLocator.ets
export class ServiceLocator {
  private static instance: ServiceLocator;
  private services: Map<string, any> = new Map();

  static getInstance(): ServiceLocator {
    if (!ServiceLocator.instance) {
      ServiceLocator.instance = new ServiceLocator();
    }
    return ServiceLocator.instance;
  }

  register<T>(key: string, service: T): void {
    this.services.set(key, service);
  }

  get<T>(key: string): T {
    return this.services.get(key) as T;
  }
}
```

#### 2.2.2 服务注册配置
```typescript
// ServiceConfiguration.ets
export class ServiceConfiguration {
  static configure(): void {
    const locator = ServiceLocator.getInstance();

    // 注册核心服务
    locator.register('HttpClient', new HttpClient());
    locator.register('AuthService', new AuthService());
    locator.register('StorageService', new StorageService());
    locator.register('LoggingService', new LoggingService());
  }
}
```

### 2.3 事件驱动架构

#### 2.3.1 事件总线设计
```typescript
// EventBus.ets
export interface EventListener<T = any> {
  (data: T): void;
}

export class EventBus {
  private static instance: EventBus;
  private listeners: Map<string, EventListener[]> = new Map();

  static getInstance(): EventBus {
    if (!EventBus.instance) {
      EventBus.instance = new EventBus();
    }
    return EventBus.instance;
  }

  subscribe<T>(event: string, listener: EventListener<T>): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(listener);
  }

  emit<T>(event: string, data?: T): void {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.forEach(listener => listener(data));
    }
  }

  unsubscribe(event: string, listener: EventListener): void {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      const index = eventListeners.indexOf(listener);
      if (index > -1) {
        eventListeners.splice(index, 1);
      }
    }
  }
}
```

---

## 3. 详细功能规格说明

### 3.1 认证模块详细设计

#### 3.1.1 登录页面功能规格

**页面布局规格**:
- 页面背景：渐变色背景或纯色背景，支持深色模式
- Logo区域：
  - 尺寸：120vp × 120vp
  - 位置：距离顶部20%屏幕高度
  - 样式：圆角矩形，圆角半径20vp
  - 背景：主题色渐变
  - 图标：白色企业图标，尺寸60vp

**表单组件规格**:
- 用户名输入框：
  - 占位符文本："请输入用户名"
  - 左侧图标：person图标，24vp
  - 输入验证：非空验证，最大长度50字符
  - 错误提示：红色文字，显示在输入框下方
- 密码输入框：
  - 占位符文本："请输入密码"
  - 左侧图标：lock图标，24vp
  - 右侧图标：eye/eye_off切换图标，24vp
  - 输入验证：非空验证，最小长度6字符
- 记住密码复选框：
  - 位置：表单左下角
  - 文字："记住密码"
  - 默认状态：未选中
- 服务器配置链接：
  - 位置：表单右下角
  - 文字："服务器配置"
  - 样式：主题色文字，无下划线
  - 点击行为：跳转到服务器配置页面

**登录按钮规格**:
- 尺寸：全宽度，高度48vp
- 样式：主题色背景，白色文字，圆角4vp
- 文字："登录"，字体大小16fp，加粗
- 加载状态：显示旋转加载图标，禁用点击
- 点击行为：执行登录验证流程

**服务器信息显示**:
- 位置：页面底部，距离底部安全区域16vp
- 样式：半透明背景，圆角8vp
- 内容：当前服务器名称或地址
- 点击行为：跳转到服务器配置页面

#### 3.1.2 登录流程详细设计

**前端验证流程**:
1. 用户名验证：
   - 检查是否为空
   - 检查长度是否在1-50字符之间
   - 检查是否包含特殊字符（可选）
2. 密码验证：
   - 检查是否为空
   - 检查长度是否大于等于6字符
   - 检查复杂度要求（可选）

**网络请求流程**:
1. 显示加载状态
2. 构建登录请求数据
3. 发送HTTP POST请求到登录接口
4. 处理响应结果：
   - 成功：保存用户信息和Token，跳转主页
   - 失败：显示错误信息，恢复表单状态

**数据持久化流程**:
1. 保存用户基本信息到Preferences
2. 保存登录Token到安全存储
3. 根据"记住密码"选项决定是否保存密码
4. 记录登录时间戳用于会话管理

#### 3.1.3 服务器配置功能规格

**手动配置界面**:
- 服务器地址输入框：
  - 占位符："请输入服务器地址"
  - 验证：IP地址或域名格式验证
  - 示例文本："例如：*************"
- 端口号输入框：
  - 占位符："端口号"
  - 验证：1-65535范围验证
  - 默认值：8080
- 服务器名称输入框：
  - 占位符："服务器名称（可选）"
  - 用途：便于用户识别和管理

**二维码扫描功能**:
- 扫描界面：全屏相机预览
- 扫描框：中央正方形扫描区域
- 提示文字："将二维码放入框内进行扫描"
- 扫描结果处理：
  - 解析二维码内容
  - 验证格式：ip:port,name
  - 自动填充配置表单

**已保存服务器管理**:
- 列表显示：卡片式布局
- 每个服务器项包含：
  - 服务器名称（主标题）
  - 服务器地址:端口（副标题）
  - 操作按钮：使用、编辑、删除
- 操作功能：
  - 使用：设置为当前服务器
  - 编辑：修改服务器配置
  - 删除：删除服务器配置（需确认）

**连接测试功能**:
- 测试按钮：位于配置表单底部
- 测试流程：
  1. 发送测试请求到服务器
  2. 显示测试进度
  3. 显示测试结果（成功/失败）
- 超时设置：10秒超时
- 错误处理：显示具体错误信息

### 3.2 主框架模块详细设计

#### 3.2.1 底部导航详细规格

**导航栏整体设计**:
- 高度：56vp + 底部安全区域
- 背景色：表面色（跟随主题）
- 顶部边框：1vp分割线，边框色
- 阴影：elevation 8vp

**导航项设计规格**:
- 图标尺寸：24vp × 24vp
- 文字大小：12fp
- 图标与文字间距：4vp
- 选中状态：
  - 图标颜色：主题色
  - 文字颜色：主题色
  - 文字粗细：Medium
- 未选中状态：
  - 图标颜色：次要文字色
  - 文字颜色：次要文字色
  - 文字粗细：Regular

**导航项配置**:
1. 首页 (Home)：
   - 图标：home
   - 文字：首页
   - 页面：HomePage
2. 应用 (Apps)：
   - 图标：apps
   - 文字：应用
   - 页面：AppsPage
3. 工作台 (Workspace)：
   - 图标：desktop_computer
   - 文字：工作台
   - 页面：WorkspacePage
4. 消息 (Messages)：
   - 图标：message
   - 文字：消息
   - 页面：MessagesPage
   - 特殊功能：显示未读消息数量徽章
5. 设置 (Settings)：
   - 图标：person
   - 文字：设置
   - 页面：SettingsPage

**交互行为规格**:
- 点击切换：无动画过渡，直接切换
- 页面保持：使用Tabs组件保持页面状态
- 双击行为：双击当前选中项回到页面顶部
- 长按行为：显示页面名称提示（可选）

#### 3.2.2 顶部用户信息栏详细设计

**布局结构规格**:
- 容器高度：自适应内容，最小64vp
- 内边距：16vp（左右），12vp（上下）
- 背景色：表面色
- 底部边框：1vp分割线
- 阴影：elevation 2vp

**用户头像规格**:
- 尺寸：40vp × 40vp
- 形状：圆形
- 默认图标：person，白色，尺寸24vp
- 背景色：主题色
- 点击行为：跳转到个人信息页面（可选）

**用户信息文字规格**:
- 主文字（欢迎语）：
  - 内容："欢迎回来，{用户名}"
  - 字体大小：16fp
  - 字体粗细：Medium
  - 颜色：主要文字色
  - 最大行数：1行，超出省略
- 副文字（职位信息）：
  - 内容：用户职位或部门信息
  - 字体大小：14fp
  - 字体粗细：Regular
  - 颜色：次要文字色
  - 最大行数：1行，超出省略

**通知图标规格**:
- 图标：notifications_none，24vp
- 颜色：次要文字色
- 点击区域：48vp × 48vp
- 点击行为：跳转到消息页面
- 徽章显示：
  - 位置：图标右上角
  - 尺寸：8vp × 8vp（无数字）或自适应（有数字）
  - 背景色：错误色（红色）
  - 文字色：白色
  - 字体大小：10fp

**固定行为实现**:
- 布局方式：Column布局
- 用户信息栏：固定在顶部
- 主内容区域：使用Expanded包装，可滚动
- 滚动行为：只有主内容区域滚动，用户信息栏保持固定

### 3.3 首页模块详细设计

#### 3.3.1 轮播图组件详细规格

**容器规格**:
- 高度：180vp
- 外边距：16vp（左右），8vp（上下）
- 圆角：12vp
- 背景：白色（浅色主题）/表面色（深色主题）

**轮播图片规格**:
- 尺寸：填充容器
- 圆角：12vp
- 加载状态：显示骨架屏或占位图
- 错误状态：显示默认占位图
- 图片优化：支持WebP格式，自动压缩

**自动播放配置**:
- 播放间隔：3秒
- 循环播放：是
- 用户交互暂停：用户滑动时暂停自动播放
- 恢复播放：用户停止交互3秒后恢复

**指示器规格**:
- 位置：轮播图底部中央，距离底部12vp
- 指示点尺寸：8vp × 8vp
- 指示点间距：8vp
- 选中状态：主题色，不透明
- 未选中状态：白色，50%透明度
- 动画：切换时有缩放动画

**交互行为**:
- 左右滑动：切换图片
- 点击图片：跳转到详情页面（如果有链接）
- 滑动指示：显示滑动进度

#### 3.3.2 企业资讯组件详细规格

**容器规格**:
- 外边距：16vp（左右），0vp（上下）
- 背景：卡片背景色
- 圆角：12vp
- 阴影：elevation 2vp

**标题区域规格**:
- 内边距：16vp
- 标题文字："企业资讯"
- 字体大小：18fp
- 字体粗细：SemiBold
- 颜色：主要文字色

**资讯列表规格**:
- 列表类型：垂直滚动列表
- 分割线：1vp高度，左边距16vp
- 空状态：显示"暂无资讯"提示

**资讯项规格**:
- 内边距：16vp
- 最小高度：72vp
- 点击效果：涟漪动画

**资讯项内容布局**:
- 标题：
  - 字体大小：16fp
  - 字体粗细：Medium
  - 颜色：主要文字色
  - 最大行数：2行
  - 行间距：1.2倍
- 时间：
  - 字体大小：12fp
  - 字体粗细：Regular
  - 颜色：次要文字色
  - 位置：标题右上角
- 内容摘要：
  - 字体大小：14fp
  - 字体粗细：Regular
  - 颜色：次要文字色
  - 最大行数：2行
  - 行间距：1.3倍
  - 与标题间距：8vp

#### 3.3.3 数据加载与刷新机制

**初始加载流程**:
1. 显示骨架屏动画
2. 并发请求轮播图和资讯数据
3. 数据返回后更新UI
4. 隐藏加载状态

**下拉刷新机制**:
- 触发距离：80vp
- 刷新图标：旋转动画
- 刷新文字："正在刷新..."
- 完成提示："刷新完成"
- 自动隐藏：2秒后自动隐藏

**错误处理机制**:
- 网络错误：显示重试按钮
- 数据为空：显示空状态提示
- 加载超时：显示超时提示
- 重试机制：最多重试3次

**缓存策略**:
- 轮播图：缓存24小时
- 资讯数据：缓存1小时
- 图片缓存：LRU策略，最大50MB
- 离线模式：显示缓存数据

### 3.4 应用中心模块详细设计

#### 3.4.1 应用网格布局详细规格

**网格配置**:
- 列数：3列
- 行间距：24vp
- 列间距：16vp
- 容器内边距：16vp

**应用项规格**:
- 容器尺寸：自适应宽度 × 96vp高度
- 布局：垂直居中对齐
- 点击区域：整个容器
- 点击效果：缩放动画（0.95倍）

**应用图标规格**:
- 尺寸：48vp × 48vp
- 圆角：12vp
- 默认图标：apps图标，主题色背景
- 加载状态：显示占位图标
- 错误状态：显示默认图标

**应用名称规格**:
- 字体大小：12fp
- 字体粗细：Regular
- 颜色：主要文字色
- 最大行数：1行
- 超出处理：省略号
- 与图标间距：8vp

#### 3.4.2 编辑模式详细设计

**编辑按钮规格**:
- 位置：AppBar右侧
- 图标：edit（编辑模式）/ save（保存模式）
- 尺寸：24vp
- 点击区域：48vp × 48vp

**编辑状态视觉变化**:
- 应用项抖动动画：轻微的左右摆动
- 选择框显示：右上角圆形选择框
- 选择框规格：
  - 尺寸：20vp × 20vp
  - 位置：距离右上角-6vp
  - 背景：白色边框，主题色填充（选中）
  - 图标：check，白色，12vp

**编辑操作流程**:
1. 点击编辑按钮进入编辑模式
2. 应用项开始抖动动画
3. 显示选择框
4. 用户点击应用项切换选中状态
5. 点击保存按钮确认更改
6. 退出编辑模式，停止动画

**数据保存机制**:
- 本地存储：使用Preferences保存常用应用列表
- 数据格式：JSON数组，包含应用ID和排序信息
- 同步机制：保存后同步到服务器（可选）

#### 3.4.3 搜索功能详细设计

**搜索栏规格**:
- 位置：页面顶部，AppBar下方
- 高度：48vp
- 外边距：16vp（左右），8vp（上下）
- 背景：搜索框背景色
- 圆角：24vp

**搜索输入框规格**:
- 占位符："搜索应用"
- 字体大小：16fp
- 内边距：12vp（左右）
- 左侧图标：search，20vp，次要文字色
- 右侧清除按钮：close，20vp（有内容时显示）

**搜索功能实现**:
- 实时搜索：输入时立即过滤
- 搜索范围：应用名称、应用描述
- 搜索算法：模糊匹配，支持拼音搜索
- 搜索历史：保存最近10次搜索记录
- 空结果：显示"未找到相关应用"

**搜索结果展示**:
- 布局保持：与正常网格布局一致
- 高亮显示：搜索关键词高亮
- 结果排序：按相关度排序
- 清空搜索：点击清除按钮或删除所有文字

### 3.5 工作台模块详细设计

#### 3.5.1 搜索栏设计

**固定搜索栏**:
- 位置：页面顶部，AppBar下方
- 样式：圆角搜索框，带搜索图标
- 功能：实时搜索应用
- 背景：与页面背景区分的卡片背景

#### 3.5.2 应用网格设计

**布局特性**:
- 3x3网格布局
- 应用图标：64vp × 64vp
- 应用名称：图标下方显示
- 编辑模式：支持拖拽重新排序

**交互设计**:
- 点击应用图标启动应用
- 长按进入编辑模式
- 编辑模式下支持删除和排序

### 3.6 消息模块详细设计

#### 3.6.1 消息分类设计

**顶部标签栏**:
- 全部、未读、系统、工作四个分类
- 选中状态：主色调文字和下划线
- 切换动画：平滑的标签切换动画

#### 3.6.2 消息列表设计

**列表项结构**:
- 左侧图标：消息类型图标，彩色背景
- 消息标题：加粗显示
- 消息内容：最多2行，超出省略
- 时间戳：右上角显示
- 未读标识：右侧红点提示

**交互功能**:
- 点击查看消息详情
- 左滑显示操作按钮（标记已读、删除）
- 下拉刷新获取新消息

### 3.7 设置模块详细设计

#### 3.7.1 用户信息卡片设计

**固定用户信息区域**:
- 位置：页面顶部固定，不随内容滚动
- 布局：左侧头像 + 中间用户信息 + 右侧箭头
- 头像：70vp圆形头像
- 用户信息：姓名和职位垂直排列
- 背景：卡片背景，带阴影

#### 3.7.2 设置菜单设计

**可滚动菜单区域**:
- 位置：用户信息卡片下方，可独立滚动
- 分组设计：账号设置、企业设置、应用偏好
- 菜单项结构：
  - 左侧彩色图标
  - 中间标题和副标题
  - 右侧箭头图标

**菜单分组**:
1. **账号设置**:
   - 账号安全
   - 消息通知
   - 隐私设置

2. **企业设置**:
   - 企业信息
   - 部门管理

3. **应用偏好**:
   - 应用排序
   - 外观设置
   - 语言设置
   - 服务器配置

---

## 4. UI/UX设计系统

### 4.1 色彩系统

#### 4.1.1 主色调定义
**主色调**:
- 主色：#1976D2 (蓝色)
- 主色变体：#1565C0 (深蓝)
- 次要色：#03DAC6 (青色)
- 强调色：#FF5722 (橙红)

**中性色系**:
- 背景色：#FAFAFA (浅灰)
- 表面色：#FFFFFF (白色)
- 文字主色：#212121 (深灰)
- 文字次色：#757575 (中灰)
- 文字三级：#BDBDBD (浅灰)
- 边框色：#E0E0E0 (浅灰)

**状态色系**:
- 成功色：#4CAF50 (绿色)
- 警告色：#FF9800 (橙色)
- 错误色：#F44336 (红色)
- 信息色：#2196F3 (蓝色)

#### 4.1.2 深色主题适配
**深色主题色彩**:
- 背景色：#121212
- 表面色：#1E1E1E
- 卡片色：#2D2D2D
- 文字主色：#FFFFFF
- 文字次色：#CCCCCC
- 文字三级：#999999
- 边框色：#404040

**主题切换机制**:
- 跟随系统主题设置
- 支持手动切换
- 平滑过渡动画
- 状态持久化保存

### 4.2 字体系统

#### 4.2.1 字体层级定义
**字体大小规范**:
- 超大标题：28fp (Display Large)
- 大标题：24fp (Display Medium)
- 标题：20fp (Headline Large)
- 副标题：18fp (Headline Medium)
- 正文大：16fp (Body Large)
- 正文：14fp (Body Medium)
- 说明文字：12fp (Body Small)
- 标签文字：10fp (Label Small)

**字体权重规范**:
- 超粗：FontWeight.Black (900)
- 粗体：FontWeight.Bold (700)
- 半粗：FontWeight.SemiBold (600)
- 中等：FontWeight.Medium (500)
- 常规：FontWeight.Regular (400)
- 细体：FontWeight.Light (300)

#### 4.2.2 行高与间距
**行高规范**:
- 标题类：1.2倍行高
- 正文类：1.4倍行高
- 说明类：1.3倍行高

**字符间距**:
- 标题：0.02em
- 正文：0.01em
- 标签：0.03em

### 4.3 间距系统

#### 4.3.1 标准间距定义
**基础间距单位**:
- 超小：4vp
- 小：8vp
- 中等：16vp
- 大：24vp
- 超大：32vp
- 巨大：48vp

#### 4.3.2 组件间距规范
**页面级间距**:
- 页面边距：16vp
- 区块间距：24vp
- 内容组间距：16vp

**组件级间距**:
- 列表项内边距：16vp
- 卡片内边距：16vp
- 按钮内边距：16vp垂直，24vp水平
- 输入框内边距：12vp垂直，16vp水平

### 4.4 圆角系统

#### 4.4.1 圆角规范定义
**圆角大小**:
- 无圆角：0vp
- 小圆角：4vp (按钮、输入框)
- 中圆角：8vp (卡片、对话框)
- 大圆角：12vp (轮播图、特殊卡片)
- 超大圆角：16vp (底部弹窗)
- 圆形：50% (头像、图标背景)

#### 4.4.2 圆角应用场景
**组件圆角映射**:
- 按钮：4vp
- 输入框：4vp
- 卡片：8vp
- 图片容器：12vp
- 头像：50%
- 徽章：50%

### 4.5 阴影系统

#### 4.5.1 阴影层级定义
**阴影规范**:
- 无阴影：elevation 0
- 一级阴影：elevation 2 (卡片)
- 二级阴影：elevation 4 (悬浮按钮)
- 三级阴影：elevation 8 (导航栏)
- 四级阴影：elevation 16 (对话框)
- 五级阴影：elevation 24 (抽屉)

#### 4.5.2 阴影应用场景
**组件阴影映射**:
- 卡片：elevation 2
- AppBar：elevation 4
- 悬浮按钮：elevation 6
- 底部导航：elevation 8
- 对话框：elevation 24

---

## 5. 组件库设计规范

### 5.1 基础组件规范

#### 5.1.1 按钮组件设计

**主要按钮 (Primary Button)**:
- 背景色：主题色
- 文字色：白色
- 圆角：4vp
- 高度：48vp
- 最小宽度：88vp
- 内边距：16vp垂直，24vp水平
- 点击效果：涟漪动画 + 轻微缩放
- 禁用状态：50%透明度

**次要按钮 (Secondary Button)**:
- 背景色：透明
- 边框：1vp主题色边框
- 文字色：主题色
- 其他规格同主要按钮

**文字按钮 (Text Button)**:
- 背景色：透明
- 文字色：主题色
- 无边框
- 内边距：8vp垂直，16vp水平
- 最小高度：36vp

**图标按钮 (Icon Button)**:
- 尺寸：48vp × 48vp
- 图标尺寸：24vp
- 圆形点击区域
- 涟漪动画效果

#### 5.1.2 输入框组件设计

**标准输入框 (Text Input)**:
- 边框：1vp灰色边框
- 圆角：4vp
- 内边距：12vp垂直，16vp水平
- 最小高度：48vp
- 获得焦点：主题色边框
- 错误状态：红色边框

**带图标输入框**:
- 左侧图标：24vp图标，距离左边16vp
- 文字左边距：48vp
- 右侧图标：可选，用于密码显示/隐藏

**搜索输入框**:
- 圆角：24vp
- 背景色：搜索框背景色
- 左侧搜索图标：20vp
- 右侧清除按钮：20vp

#### 5.1.3 卡片组件设计

**标准卡片 (Card)**:
- 背景：白色/深色主题下的表面色
- 圆角：8vp
- 阴影：elevation 2
- 内边距：16vp
- 外边距：16vp

**信息卡片**:
- 带标题区域
- 内容区域可滚动
- 支持操作按钮

**列表卡片**:
- 包含多个列表项
- 统一的分割线样式
- 支持展开/收起

#### 5.1.4 列表组件设计

**标准列表项 (List Item)**:
- 最小高度：56vp
- 内边距：16vp垂直，16vp水平
- 分割线：1vp高度，左边距16vp
- 点击效果：涟漪动画

**带图标列表项**:
- 左侧图标：24vp，距离左边16vp
- 文字左边距：56vp
- 右侧箭头：可选

**多行列表项**:
- 主标题：16fp，Medium
- 副标题：14fp，Regular
- 标题间距：4vp

### 5.2 导航组件规范

#### 5.2.1 AppBar组件

**标准AppBar**:
- 高度：56vp
- 背景：主题色
- 标题：白色文字，20fp，Medium
- 返回按钮：白色图标，24vp
- 操作按钮：白色图标，48vp点击区域

**透明AppBar**:
- 背景：透明
- 文字色：根据背景自适应
- 状态栏适配

#### 5.2.2 底部导航组件

**BottomNavigationBar**:
- 高度：56vp + 安全区域
- 背景：表面色
- 图标：24vp
- 文字：12fp
- 选中色：主题色
- 未选中色：次要文字色

**TabBar**:
- 高度：48vp
- 指示器：2vp高度，主题色
- 文字：16fp
- 选中状态：主题色，Medium
- 未选中状态：次要文字色，Regular

### 5.3 反馈组件规范

#### 5.3.1 Toast组件

**标准Toast**:
- 背景：半透明黑色 (rgba(0,0,0,0.8))
- 文字：白色，14fp
- 圆角：4vp
- 内边距：12vp垂直，16vp水平
- 显示时长：2秒
- 位置：屏幕底部，距离底部80vp

**成功Toast**:
- 背景：成功色
- 图标：check_circle，白色

**错误Toast**:
- 背景：错误色
- 图标：error，白色

#### 5.3.2 对话框组件

**确认对话框**:
- 背景：表面色
- 圆角：8vp
- 最大宽度：320vp
- 标题：20fp，SemiBold
- 内容：16fp，Regular
- 按钮：文字按钮，右对齐

**输入对话框**:
- 包含输入框
- 支持验证
- 确认/取消按钮

#### 5.3.3 加载组件

**加载指示器**:
- 圆形进度条：主题色
- 大小：24vp (小) / 32vp (中) / 48vp (大)
- 动画：顺时针旋转，1秒一圈

**骨架屏**:
- 背景：浅灰色
- 动画：从左到右的光泽效果
- 形状：模拟实际内容布局

### 5.4 数据展示组件

#### 5.4.1 轮播图组件

**Carousel**:
- 自动播放：支持
- 指示器：圆点样式
- 无限循环：支持
- 手势控制：支持左右滑动

#### 5.4.2 网格组件

**Grid**:
- 响应式列数
- 等高布局
- 支持拖拽排序
- 空状态处理

#### 5.4.3 徽章组件

**Badge**:
- 圆形：8vp × 8vp (无数字)
- 椭圆形：自适应宽度 × 16vp高度 (有数字)
- 背景：错误色
- 文字：白色，10fp
- 位置：相对于父组件的右上角

---

## 6. 数据架构与API设计

### 6.1 数据模型设计

#### 6.1.1 用户相关模型
```typescript
// 用户信息模型
interface UserInfo {
  id: number;
  username: string;
  name: string;
  avatar: string;
  department: string;
  title: string;
  email?: string;
  phone?: string;
}

// 登录请求模型
interface LoginRequest {
  username: string;
  password: string;
  serverAddress?: string;
  rememberPassword: boolean;
}

// 登录响应模型
interface LoginResponse {
  success: boolean;
  message: string;
  data?: {
    token: string;
    user: UserInfo;
    expiresIn: number;
  };
}
```

#### 6.1.2 应用相关模型
```typescript
// 应用信息模型
interface AppInfo {
  id: string;
  name: string;
  icon: string;
  description: string;
  category: string;
  version: string;
  isFavorite: boolean;
  sortOrder: number;
  url?: string;
  packageName?: string;
}

// 应用分类模型
interface AppCategory {
  id: string;
  name: string;
  icon: string;
  sortOrder: number;
}
```
