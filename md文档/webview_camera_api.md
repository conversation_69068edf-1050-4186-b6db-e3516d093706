# WebView 相机功能 API 文档

## 功能概述
提供 WebView 中调用原生相机功能的完整解决方案，支持拍照、录像、相册选择等功能。

## JavaScript API

### 拍照功能
```javascript
// 基础拍照
const result = await CameraAPI.takePhoto();

// 自定义选项拍照
const result = await CameraAPI.takePhoto({
    quality: 80,           // 图片质量 (0-100)
    maxWidth: 1024,        // 最大宽度
    maxHeight: 1024,       // 最大高度
    source: 'camera'       // 来源: camera, gallery
});
```

### 录像功能
```javascript
const result = await CameraAPI.recordVideo({
    maxDuration: 60,       // 最大录制时长(秒)
    quality: 'medium'      // 视频质量: low, medium, high
});
```

### 相册选择
```javascript
const result = await CameraAPI.pickFromGallery({
    multiple: true,        // 是否多选
    maxCount: 5           // 最大选择数量
});
```

## Flutter 实现

### 依赖配置
```yaml
dependencies:
  image_picker: ^1.0.4
  camera: ^0.10.5
  permission_handler: ^11.3.1
```

### 权限配置
```xml
<!-- Android -->
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
```

### 核心实现
```dart
class CameraPlugin implements NativePlugin {
  @override
  String get name => 'camera';
  
  @override
  Future<dynamic> execute(String method, Map<String, dynamic> params) async {
    switch (method) {
      case 'takePhoto':
        return await _takePhoto(params);
      case 'recordVideo':
        return await _recordVideo(params);
      case 'pickFromGallery':
        return await _pickFromGallery(params);
    }
  }
  
  Future<Map<String, dynamic>> _takePhoto(Map<String, dynamic> params) async {
    final picker = ImagePicker();
    final source = params['source'] == 'gallery' 
        ? ImageSource.gallery 
        : ImageSource.camera;
    
    final XFile? image = await picker.pickImage(
      source: source,
      maxWidth: params['maxWidth']?.toDouble(),
      maxHeight: params['maxHeight']?.toDouble(),
      imageQuality: params['quality'] ?? 80,
    );
    
    if (image != null) {
      return {
        'path': image.path,
        'name': image.name,
        'size': await image.length(),
      };
    }
    
    throw Exception('拍照被取消');
  }
}
```

## 使用示例

### HTML 页面集成
```html
<button onclick="takePhoto()">拍照</button>
<img id="photo-preview" style="max-width: 300px;" />

<script>
async function takePhoto() {
    try {
        const result = await CameraAPI.takePhoto({
            quality: 80,
            maxWidth: 800
        });
        
        // 显示拍照结果
        document.getElementById('photo-preview').src = 'file://' + result.path;
        console.log('拍照成功:', result);
    } catch (error) {
        alert('拍照失败: ' + error.message);
    }
}
</script>
```

## 错误处理

### 常见错误类型
- `PERMISSION_DENIED`: 相机权限被拒绝
- `CAMERA_NOT_AVAILABLE`: 相机不可用
- `OPERATION_CANCELLED`: 用户取消操作
- `FILE_SAVE_ERROR`: 文件保存失败

### 错误处理示例
```javascript
try {
    const result = await CameraAPI.takePhoto();
} catch (error) {
    switch (error.code) {
        case 'PERMISSION_DENIED':
            alert('请允许访问相机权限');
            break;
        case 'CAMERA_NOT_AVAILABLE':
            alert('相机不可用，请检查设备');
            break;
        default:
            alert('拍照失败: ' + error.message);
    }
}
```

## 最佳实践

1. **权限检查**: 使用前检查相机权限
2. **文件管理**: 及时清理临时文件
3. **内存优化**: 大图片进行压缩处理
4. **用户体验**: 提供加载状态和进度提示
5. **错误处理**: 完善的错误提示和重试机制