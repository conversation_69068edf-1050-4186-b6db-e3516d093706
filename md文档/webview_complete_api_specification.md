# WebView 完整功能 API 规范文档

## 1. 功能概述

本文档定义了企业混合App中WebView的完整功能规范，包括8大核心功能模块：

1. **相机功能** - 拍照、录像、相册选择
2. **二维码扫描** - 扫描、生成、批量处理
3. **蓝牙打印机** - 设备连接、文档打印
4. **NFC功能** - 标签读写、数据交换
5. **定位服务** - 位置获取、地址解析
6. **设备信息** - 设备标识、用户信息
7. **标题栏控制** - 动态按钮、页面导航
8. **系统集成** - 权限管理、错误处理

## 2. 架构设计

### 2.1 整体架构

```
┌─────────────────────────────────────────┐
│                WebView                  │
│  ┌─────────────────────────────────────┐│
│  │         JavaScript API              ││
│  │  ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐   ││
│  │  │相机 │ │扫码 │ │蓝牙 │ │NFC  │   ││
│  │  └─────┘ └─────┘ └─────┘ └─────┘   ││
│  │  ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐   ││
│  │  │定位 │ │设备 │ │标题 │ │系统 │   ││
│  │  └─────┘ └─────┘ └─────┘ └─────┘   ││
│  └─────────────────────────────────────┘│
└─────────────────────────────────────────┘
                    ↕
┌─────────────────────────────────────────┐
│            Native Bridge                │
│  ┌─────────────────────────────────────┐│
│  │         Plugin Manager              ││
│  │  ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐   ││
│  │  │相机 │ │扫码 │ │蓝牙 │ │NFC  │   ││
│  │  └─────┘ └─────┘ └─────┘ └─────┘   ││
│  │  ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐   ││
│  │  │定位 │ │设备 │ │标题 │ │系统 │   ││
│  │  └─────┘ └─────┘ └─────┘ └─────┘   ││
│  └─────────────────────────────────────┘│
└─────────────────────────────────────────┘
                    ↕
┌─────────────────────────────────────────┐
│           Flutter Native                │
│  ┌─────────────────────────────────────┐│
│  │        System Services              ││
│  │  ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐   ││
│  │  │权限 │ │存储 │ │网络 │ │硬件 │   ││
│  │  └─────┘ └─────┘ └─────┘ └─────┘   ││
│  └─────────────────────────────────────┘│
└─────────────────────────────────────────┘
```

### 2.2 插件系统架构

```dart
// 插件接口定义
abstract class NativePlugin {
  String get name;                    // 插件名称
  List<String> get methods;           // 支持的方法
  List<String> get permissions;       // 需要的权限
  Future<dynamic> execute(String method, Map<String, dynamic> params);
  Future<bool> checkPermissions();    // 权限检查
  Future<void> requestPermissions();  // 权限请求
}

// 插件管理器
class PluginManager {
  static final Map<String, NativePlugin> _plugins = {};
  
  static void registerPlugin(NativePlugin plugin);
  static Future<dynamic> executeMethod(String method, Map<String, dynamic> params);
  static Future<bool> checkAllPermissions();
  static Map<String, List<String>> getAllPlugins();
}
```

## 3. JavaScript API 规范

### 3.1 全局API对象

```javascript
// 全局API对象结构
window.NativeAPI = {
  // 相机功能
  Camera: {
    takePhoto: (options) => Promise,
    recordVideo: (options) => Promise,
    pickFromGallery: (options) => Promise
  },
  
  // 二维码功能
  QRCode: {
    scan: (options) => Promise,
    generate: (data, options) => Promise,
    scanMultiple: (options) => Promise
  },
  
  // 蓝牙打印机
  Bluetooth: {
    scanDevices: (timeout) => Promise,
    connect: (deviceId) => Promise,
    disconnect: () => Promise,
    printText: (text, options) => Promise,
    printImage: (imagePath, options) => Promise,
    printQRCode: (data, options) => Promise
  },
  
  // NFC功能
  NFC: {
    isAvailable: () => Promise,
    startScan: (options) => Promise,
    stopScan: () => Promise,
    readTag: () => Promise,
    writeTag: (type, data) => Promise
  },
  
  // 定位服务
  Location: {
    getCurrentPosition: (options) => Promise,
    watchPosition: (callback, options) => Promise,
    clearWatch: (watchId) => Promise,
    geocode: (address) => Promise,
    reverseGeocode: (lat, lng) => Promise
  },
  
  // 设备信息
  Device: {
    getDeviceId: () => Promise,
    getDeviceInfo: () => Promise,
    getUserInfo: () => Promise,
    getAppInfo: () => Promise
  },
  
  // 标题栏控制
  TitleBar: {
    setTitle: (title) => Promise,
    setRightButton: (options) => Promise,
    hideRightButton: () => Promise,
    showBackButton: (show) => Promise
  },
  
  // 系统功能
  System: {
    showToast: (message, duration) => Promise,
    showAlert: (title, message) => Promise,
    showConfirm: (title, message) => Promise,
    vibrate: (pattern) => Promise,
    openUrl: (url) => Promise,
    shareContent: (content, options) => Promise
  }
};
```

### 3.2 统一调用方式

```javascript
// 统一的原生方法调用
async function callNative(method, params = {}) {
  try {
    const result = await window.nativeBridge.callNative(method, params);
    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// 使用示例
const result = await callNative('camera.takePhoto', {
  quality: 80,
  maxWidth: 1024,
  maxHeight: 1024
});

if (result.success) {
  console.log('拍照成功:', result.data);
} else {
  console.error('拍照失败:', result.error);
}
```

## 4. 权限管理规范

### 4.1 权限分类

```yaml
# 权限配置映射
permissions:
  camera:
    android:
      - android.permission.CAMERA
      - android.permission.WRITE_EXTERNAL_STORAGE
    ios:
      - NSCameraUsageDescription
      - NSPhotoLibraryUsageDescription
  
  location:
    android:
      - android.permission.ACCESS_FINE_LOCATION
      - android.permission.ACCESS_COARSE_LOCATION
    ios:
      - NSLocationWhenInUseUsageDescription
      - NSLocationAlwaysAndWhenInUseUsageDescription
  
  bluetooth:
    android:
      - android.permission.BLUETOOTH
      - android.permission.BLUETOOTH_ADMIN
      - android.permission.BLUETOOTH_CONNECT
      - android.permission.BLUETOOTH_SCAN
      - android.permission.ACCESS_FINE_LOCATION
    ios:
      - NSBluetoothAlwaysUsageDescription
  
  nfc:
    android:
      - android.permission.NFC
    ios:
      - com.apple.developer.nfc.readersession.formats
```

### 4.2 权限检查流程

```dart
class PermissionManager {
  static Future<bool> checkPermission(String permission) async {
    final status = await Permission.values
        .firstWhere((p) => p.toString().contains(permission))
        .status;
    return status.isGranted;
  }
  
  static Future<bool> requestPermission(String permission) async {
    final result = await Permission.values
        .firstWhere((p) => p.toString().contains(permission))
        .request();
    return result.isGranted;
  }
  
  static Future<Map<String, bool>> checkPluginPermissions(String pluginName) async {
    final plugin = PluginManager.getPlugin(pluginName);
    final results = <String, bool>{};
    
    for (final permission in plugin.permissions) {
      results[permission] = await checkPermission(permission);
    }
    
    return results;
  }
}
```

## 5. 错误处理规范

### 5.1 错误代码定义

```javascript
const ErrorCodes = {
  // 通用错误
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  FEATURE_NOT_SUPPORTED: 'FEATURE_NOT_SUPPORTED',
  TIMEOUT: 'TIMEOUT',
  
  // 相机错误
  CAMERA_NOT_AVAILABLE: 'CAMERA_NOT_AVAILABLE',
  CAMERA_PERMISSION_DENIED: 'CAMERA_PERMISSION_DENIED',
  
  // 蓝牙错误
  BLUETOOTH_NOT_ENABLED: 'BLUETOOTH_NOT_ENABLED',
  BLUETOOTH_DEVICE_NOT_FOUND: 'BLUETOOTH_DEVICE_NOT_FOUND',
  BLUETOOTH_CONNECTION_FAILED: 'BLUETOOTH_CONNECTION_FAILED',
  
  // NFC错误
  NFC_NOT_AVAILABLE: 'NFC_NOT_AVAILABLE',
  NFC_DISABLED: 'NFC_DISABLED',
  NFC_TAG_NOT_FOUND: 'NFC_TAG_NOT_FOUND',
  
  // 定位错误
  LOCATION_PERMISSION_DENIED: 'LOCATION_PERMISSION_DENIED',
  LOCATION_SERVICE_DISABLED: 'LOCATION_SERVICE_DISABLED',
  LOCATION_TIMEOUT: 'LOCATION_TIMEOUT'
};
```

### 5.2 统一错误处理

```javascript
class APIError extends Error {
  constructor(code, message, details = null) {
    super(message);
    this.code = code;
    this.details = details;
    this.timestamp = new Date().toISOString();
  }
}

// 错误处理包装器
function handleAPICall(apiCall) {
  return apiCall.catch(error => {
    // 记录错误日志
    console.error('API调用失败:', {
      error: error.message,
      code: error.code,
      timestamp: error.timestamp,
      stack: error.stack
    });
    
    // 抛出标准化错误
    throw new APIError(
      error.code || ErrorCodes.UNKNOWN_ERROR,
      error.message || '未知错误',
      error.details
    );
  });
}
```

## 6. 开发规范

### 6.1 插件开发规范

1. **命名规范**
   - 插件类名：`{功能名}Plugin`
   - 方法名：使用驼峰命名法
   - 参数名：使用描述性名称

2. **返回值规范**
   - 成功：返回具体数据或操作结果
   - 失败：抛出包含错误码和消息的异常

3. **异步处理**
   - 所有插件方法必须返回`Future`
   - 长时间操作需要支持取消机制
   - 提供进度回调（如适用）

### 6.2 JavaScript API规范

1. **API设计原则**
   - 保持接口简洁一致
   - 提供合理的默认值
   - 支持链式调用（如适用）

2. **参数验证**
   - 必需参数检查
   - 类型验证
   - 范围验证

3. **错误处理**
   - 统一的错误格式
   - 详细的错误信息
   - 错误恢复建议

## 7. 测试规范

### 7.1 单元测试

```dart
// 插件单元测试示例
class CameraPluginTest {
  test('takePhoto should return image path', () async {
    final plugin = CameraPlugin();
    final result = await plugin.execute('takePhoto', {
      'quality': 80,
      'maxWidth': 1024
    });
    
    expect(result, isA<Map<String, dynamic>>());
    expect(result['path'], isNotNull);
  });
}
```

### 7.2 集成测试

```javascript
// JavaScript集成测试示例
describe('Camera API', () => {
  test('takePhoto should work correctly', async () => {
    const result = await NativeAPI.Camera.takePhoto({
      quality: 80,
      maxWidth: 1024
    });
    
    expect(result.success).toBe(true);
    expect(result.data.path).toBeDefined();
  });
});
```

## 8. 部署配置

### 8.1 依赖管理

```yaml
# pubspec.yaml 核心依赖
dependencies:
  # WebView
  webview_flutter: ^4.13.0
  
  # 相机和图片
  image_picker: ^1.0.4
  camera: ^0.10.5
  
  # 二维码
  mobile_scanner: ^5.2.3
  qr_flutter: ^4.1.0
  
  # 蓝牙
  bluetooth_thermal_printer: ^0.0.6
  flutter_bluetooth_serial: ^0.4.0
  
  # NFC
  nfc_manager: ^3.3.0
  
  # 定位
  geolocator: ^10.1.0
  geocoding: ^3.0.0
  
  # 权限
  permission_handler: ^11.3.1
  
  # 设备信息
  device_info_plus: ^10.1.0
  package_info_plus: ^8.0.0
```

### 8.2 平台配置

详细的Android和iOS配置将在后续的实现文档中提供。

## 9. 版本管理

- **当前版本**: v1.0.0
- **兼容性**: Flutter 3.0+, Android 6.0+, iOS 12.0+
- **更新策略**: 语义化版本控制

## 10. 后续计划

1. 完成所有插件的实现
2. 编写详细的使用文档
3. 创建示例项目
4. 性能优化和测试
5. 发布稳定版本
