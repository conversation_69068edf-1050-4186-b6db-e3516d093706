# WebView 蓝牙打印机 API 文档

## 功能概述
提供 WebView 中连接和控制蓝牙打印机的完整解决方案，支持设备扫描、连接管理、文本打印、图片打印等功能。

## JavaScript API

### 设备管理
```javascript
// 扫描蓝牙设备
const devices = await BluetoothAPI.scanDevices(10000); // 扫描10秒

// 连接设备
const connected = await BluetoothAPI.connect(deviceId);

// 断开连接
await BluetoothAPI.disconnect();

// 获取连接状态
const status = await BluetoothAPI.getConnectionStatus();
```

### 打印功能
```javascript
// 打印文本
await BluetoothAPI.printText('Hello World', {
    fontSize: 'large',     // normal, large, small
    align: 'center',       // left, center, right
    bold: true            // 是否粗体
});

// 打印图片
await BluetoothAPI.printImage('/path/to/image.jpg', {
    width: 384,           // 打印宽度
    align: 'center'       // 对齐方式
});

// 打印二维码
await BluetoothAPI.printQRCode('https://example.com', {
    size: 200,            // 二维码大小
    align: 'center'
});
```

## Flutter 实现

### 依赖配置
```yaml
dependencies:
  bluetooth_thermal_printer: ^0.0.6
  permission_handler: ^11.3.1
  flutter_bluetooth_serial: ^0.4.0
```

### 权限配置
```xml
<!-- Android -->
<uses-permission android:name="android.permission.BLUETOOTH" />
<uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
<uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
<uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
```

### 核心实现
```dart
class BluetoothPlugin implements NativePlugin {
  @override
  String get name => 'bluetooth';
  
  BluetoothConnection? _connection;
  
  Future<List<Map<String, dynamic>>> _scanDevices(Map<String, dynamic> params) async {
    // 检查蓝牙权限
    await _checkBluetoothPermissions();
    
    final devices = await BluetoothThermalPrinter.getBluetooths;
    return devices.map((device) => {
      'id': device.macAdress,
      'name': device.name ?? 'Unknown Device',
      'address': device.macAdress,
      'type': device.type,
    }).toList();
  }
  
  Future<bool> _connect(Map<String, dynamic> params) async {
    final deviceId = params['deviceId'] as String;
    
    try {
      final result = await BluetoothThermalPrinter.connect(deviceId);
      return result == "true";
    } catch (e) {
      throw Exception('连接失败: ${e.toString()}');
    }
  }
  
  Future<bool> _printText(Map<String, dynamic> params) async {
    final text = params['text'] as String;
    final options = params;
    
    String receipt = _buildPrintCommand(text, options);
    
    final result = await BluetoothThermalPrinter.writeBytes(receipt.codeUnits);
    return result == "true";
  }
  
  String _buildPrintCommand(String text, Map<String, dynamic> options) {
    String command = '';
    
    // 设置对齐方式
    final align = options['align'] as String? ?? 'left';
    switch (align) {
      case 'center':
        command += '\x1B\x61\x01';
        break;
      case 'right':
        command += '\x1B\x61\x02';
        break;
      default:
        command += '\x1B\x61\x00';
    }
    
    // 设置字体大小
    final fontSize = options['fontSize'] as String? ?? 'normal';
    if (fontSize == 'large') {
      command += '\x1D\x21\x11';
    } else if (fontSize == 'small') {
      command += '\x1D\x21\x00';
    }
    
    // 设置粗体
    final bold = options['bold'] as bool? ?? false;
    if (bold) command += '\x1B\x45\x01';
    
    command += text + '\n';
    
    // 重置格式
    command += '\x1B\x45\x00\x1D\x21\x00\x1B\x61\x00';
    
    return command;
  }
}
```

## 使用示例

### 完整打印流程
```html
<div id="bluetooth-demo">
    <button onclick="scanAndConnect()">扫描并连接</button>
    <button onclick="printReceipt()">打印小票</button>
    <button onclick="disconnect()">断开连接</button>
    <div id="status"></div>
</div>

<script>
let connectedDevice = null;

async function scanAndConnect() {
    try {
        updateStatus('正在扫描蓝牙设备...');
        
        const devices = await BluetoothAPI.scanDevices(10000);
        
        if (devices.length === 0) {
            updateStatus('未找到蓝牙设备');
            return;
        }
        
        // 显示设备列表供用户选择
        const deviceList = devices.map((device, index) => 
            `${index + 1}. ${device.name} (${device.address})`
        ).join('\n');
        
        const selection = prompt(`选择要连接的设备:\n${deviceList}\n请输入序号:`);
        const deviceIndex = parseInt(selection) - 1;
        
        if (deviceIndex >= 0 && deviceIndex < devices.length) {
            const device = devices[deviceIndex];
            updateStatus(`正在连接 ${device.name}...`);
            
            const connected = await BluetoothAPI.connect(device.id);
            
            if (connected) {
                connectedDevice = device;
                updateStatus(`已连接到 ${device.name}`);
            } else {
                updateStatus('连接失败');
            }
        }
        
    } catch (error) {
        updateStatus(`扫描失败: ${error.message}`);
    }
}

async function printReceipt() {
    if (!connectedDevice) {
        alert('请先连接打印机');
        return;
    }
    
    try {
        updateStatus('正在打印...');
        
        // 打印标题
        await BluetoothAPI.printText('购物小票', {
            fontSize: 'large',
            align: 'center',
            bold: true
        });
        
        await BluetoothAPI.printText('------------------------', {
            align: 'center'
        });
        
        // 打印商品列表
        const items = [
            { name: '苹果', price: '5.00', qty: '2' },
            { name: '香蕉', price: '3.00', qty: '1' },
            { name: '橙子', price: '4.50', qty: '3' }
        ];
        
        for (const item of items) {
            await BluetoothAPI.printText(
                `${item.name.padEnd(10)} ${item.qty}x${item.price}`, 
                { align: 'left' }
            );
        }
        
        await BluetoothAPI.printText('------------------------', {
            align: 'center'
        });
        
        // 打印总计
        await BluetoothAPI.printText('总计: ¥26.50', {
            fontSize: 'large',
            align: 'right',
            bold: true
        });
        
        // 打印二维码
        await BluetoothAPI.printQRCode('https://example.com/receipt/123', {
            size: 150,
            align: 'center'
        });
        
        await BluetoothAPI.printText('\n感谢您的光临!\n\n', {
            align: 'center'
        });
        
        updateStatus('打印完成');
        
    } catch (error) {
        updateStatus(`打印失败: ${error.message}`);
    }
}

async function disconnect() {
    try {
        await BluetoothAPI.disconnect();
        connectedDevice = null;
        updateStatus('已断开连接');
    } catch (error) {
        updateStatus(`断开连接失败: ${error.message}`);
    }
}

function updateStatus(message) {
    document.getElementById('status').textContent = message;
    console.log('蓝牙状态:', message);
}
</script>
```

## 打印命令参考

### ESC/POS 命令
```javascript
const ESC_POS = {
    // 初始化
    INIT: '\x1B\x40',
    
    // 对齐
    ALIGN_LEFT: '\x1B\x61\x00',
    ALIGN_CENTER: '\x1B\x61\x01',
    ALIGN_RIGHT: '\x1B\x61\x02',
    
    // 字体
    FONT_NORMAL: '\x1D\x21\x00',
    FONT_LARGE: '\x1D\x21\x11',
    FONT_BOLD_ON: '\x1B\x45\x01',
    FONT_BOLD_OFF: '\x1B\x45\x00',
    
    // 切纸
    CUT_PAPER: '\x1D\x56\x00',
    
    // 换行
    LINE_FEED: '\x0A',
    
    // 制表符
    TAB: '\x09'
};
```

## 故障排除

### 常见问题
1. **扫描不到设备**: 检查蓝牙权限和位置权限
2. **连接失败**: 确认设备配对状态和距离
3. **打印乱码**: 检查字符编码和打印机兼容性
4. **打印不完整**: 检查数据传输速度和缓冲区

### 调试技巧
```javascript
// 启用调试模式
BluetoothAPI.setDebugMode(true);

// 监听连接状态变化
BluetoothAPI.onConnectionStateChanged((state) => {
    console.log('连接状态变化:', state);
});

// 监听打印进度
BluetoothAPI.onPrintProgress((progress) => {
    console.log('打印进度:', progress);
});
```