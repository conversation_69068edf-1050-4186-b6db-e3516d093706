import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'dart:io';
import 'package:path_provider/path_provider.dart';

class IconGenerator {
  static Future<void> generateAppIcon() async {
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    final size = const Size(1024, 1024);
    
    // 绘制背景渐变
    final backgroundPaint = Paint()
      ..shader = const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          Color(0xFF4A90E2),
          Color(0xFF357ABD),
          Color(0xFF2E5B8A),
        ],
      ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));
    
    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), backgroundPaint);
    
    // 绘制圆形背景
    final circlePaint = Paint()
      ..color = Colors.white.withOpacity(0.1)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 8;
    
    canvas.drawCircle(
      Offset(size.width / 2, size.height / 2),
      size.width * 0.4,
      circlePaint,
    );
    
    // 绘制中心图标 - Shell
    final shellPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;
    
    final shellRect = RRect.fromRectAndRadius(
      Rect.fromCenter(
        center: Offset(size.width / 2, size.height * 0.35),
        width: size.width * 0.3,
        height: size.height * 0.2,
      ),
      const Radius.circular(20),
    );
    canvas.drawRRect(shellRect, shellPaint);
    
    // 绘制 WebView 图标
    final webPaint = Paint()
      ..color = const Color(0xFF50C878)
      ..style = PaintingStyle.fill;
    
    final webRect = RRect.fromRectAndRadius(
      Rect.fromCenter(
        center: Offset(size.width * 0.35, size.height * 0.6),
        width: size.width * 0.15,
        height: size.height * 0.1,
      ),
      const Radius.circular(8),
    );
    canvas.drawRRect(webRect, webPaint);
    
    // 绘制设备图标
    final devicePaint = Paint()
      ..color = const Color(0xFFFF6B35)
      ..style = PaintingStyle.fill;
    
    final deviceRect = RRect.fromRectAndRadius(
      Rect.fromCenter(
        center: Offset(size.width * 0.65, size.height * 0.6),
        width: size.width * 0.12,
        height: size.height * 0.15,
      ),
      const Radius.circular(12),
    );
    canvas.drawRRect(deviceRect, devicePaint);
    
    // 绘制连接线
    final linePaint = Paint()
      ..color = Colors.white.withOpacity(0.8)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 6;
    
    // WebView 到设备的连接线
    canvas.drawLine(
      Offset(size.width * 0.42, size.height * 0.6),
      Offset(size.width * 0.58, size.height * 0.6),
      linePaint,
    );
    
    // Shell 到 WebView 的连接线
    canvas.drawLine(
      Offset(size.width * 0.45, size.height * 0.45),
      Offset(size.width * 0.35, size.height * 0.55),
      linePaint,
    );
    
    // Shell 到设备的连接线
    canvas.drawLine(
      Offset(size.width * 0.55, size.height * 0.45),
      Offset(size.width * 0.65, size.height * 0.55),
      linePaint,
    );
    
    // 绘制文字
    final textPainter = TextPainter(
      text: const TextSpan(
        text: 'shell4app',
        style: TextStyle(
          color: Colors.white,
          fontSize: 80,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        (size.width - textPainter.width) / 2,
        size.height * 0.75,
      ),
    );
    
    // 绘制副标题
    final subtitlePainter = TextPainter(
      text: const TextSpan(
        text: 'WebView + Device',
        style: TextStyle(
          color: Colors.white70,
          fontSize: 40,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    
    subtitlePainter.layout();
    subtitlePainter.paint(
      canvas,
      Offset(
        (size.width - subtitlePainter.width) / 2,
        size.height * 0.82,
      ),
    );
    
    final picture = recorder.endRecording();
    final img = await picture.toImage(size.width.toInt(), size.height.toInt());
    final byteData = await img.toByteData(format: ui.ImageByteFormat.png);
    final pngBytes = byteData!.buffer.asUint8List();
    
    // 保存到文件
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/app_icon.png');
    await file.writeAsBytes(pngBytes);
    
    print('图标已生成: ${file.path}');
  }
}
