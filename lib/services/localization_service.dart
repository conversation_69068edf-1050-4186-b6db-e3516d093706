import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LocalizationService {
  static const String _languageKey = 'selected_language';
  static const String defaultLanguage = 'zh';

  static final Map<String, Map<String, String>> _localizedStrings = {
    'zh': {
      // 通用
      'app_name': '企业移动办公系统',
      'confirm': '确认',
      'cancel': '取消',
      'save': '保存',
      'edit': '编辑',
      'delete': '删除',
      'loading': '加载中...',
      'success': '成功',
      'error': '错误',
      'warning': '警告',
      'info': '信息',
      'ok': '确定',
      'close': '关闭',
      'back': '返回',
      'next': '下一步',
      'previous': '上一步',
      'refresh': '刷新',
      'search': '搜索',
      'clear': '清除',
      'select_all': '全选',
      'deselect_all': '取消全选',
      'copy': '复制',
      'copy_all': '复制全部',
      'log_statistics': '日志统计',

      // 登录页面
      'login': '登录',
      'username': '用户名',
      'password': '密码',
      'remember_password': '记住密码',
      'login_button': '登录',
      'server_config': '服务器配置',
      'login_failed': '登录失败',
      'invalid_credentials': '用户名或密码错误',
      'network_error': '网络连接错误',
      'server_error': '服务器错误',
      'login_success': '登录成功',
      'enter_username': '请输入用户名',
      'enter_password': '请输入密码',
      'username_required': '请输入用户名',
      'password_required': '请输入密码',
      'login_network_error': '登录失败，请检查网络连接',
      'current_server': '当前服务器',
      'edit_server': '编辑',
      'copyright': '版权所有 © 2023 企业移动办公系统',
      'version': '版本',
      'build_time': '构建时间',
      'test_login_persistence': '测试登录持久化',
      'test_started': '开始登录持久化测试...',
      'test_completed': '测试完成，请查看控制台输出',
      'connection_success': '连接成功',
      'use_mock_login': '使用模拟登录（勾选则使用模拟数据，取消勾选则使用真实接口）',

      // 主页面
      'home': '首页',
      'apps': '应用',
      'workspace': '工作台',
      'messages': '消息',
      'settings': '设置',
      'my': '我的',

      // 首页
      'welcome': '欢迎',
      'good_morning': '早上好',
      'good_afternoon': '下午好',
      'good_evening': '晚上好',
      'latest_news': '最新资讯',
      'company_news': '企业资讯',
      'quick_actions': '快捷操作',
      'recent_apps': '最近使用',
      'view_all': '查看全部',
      'view_more': '查看更多',
      'no_data': '暂无数据',
      'no_news': '暂无资讯',
      'loading_news': '正在加载资讯...',

      // 工作台
      'frequent_apps': '常用应用',
      'no_frequent_apps': '暂无常用应用',
      'no_frequent_apps_hint': '请联系管理员配置常用应用',
      'search_apps': '搜索应用',
      'search_messages': '搜索消息',
      'app_categories': '应用分类',
      'all_apps': '全部应用',
      'office_apps': '办公应用',
      'communication_apps': '沟通应用',
      'business_apps': '业务应用',
      'tool_apps': '工具应用',
      'drag_to_sort': '拖拽排序',
      'add_to_workspace': '添加到工作台',
      'remove_from_workspace': '从工作台移除',
      'edit_mode': '编辑模式',
      'save_changes': '保存更改',
      'changes_saved': '更改已保存',
      'sort_apps': '应用排序',
      'drag_to_reorder': '拖拽重新排序',
      'adjust_app_order': '调整应用顺序',
      'select_new_position': '选择新位置：',
      'move_forward': '向前移动',
      'move_backward': '向后移动',
      'app_position_adjusted': '应用位置已调整',
      'already_first_last': '已经是第一个/最后一个应用',
      'moved_forward_to_position': '已向前移动到位置',
      'moved_backward_to_position': '已向后移动到位置',
      'app_not_found': '应用未找到',

      // 消息
      'all_messages': '全部',
      'unread_messages': '未读',
      'system_messages': '系统',
      'work_messages': '工作',
      'mark_as_read': '标记已读',
      'reply': '回复',
      'forward': '转发',
      'message_details': '消息详情',
      'no_messages': '暂无消息',
      'new_message': '新消息',
      'send_message': '发送消息',

      // 设置
      'account_settings': '账号设置',
      'enterprise_settings': '企业设置',
      'app_preferences': '应用偏好',
      'about_help': '关于与帮助',
      'account_security': '账号安全',
      'message_notifications': '消息通知',
      'privacy_settings': '隐私设置',
      'enterprise_info': '企业信息',
      'department_management': '部门管理',
      'app_sorting': '应用排序',
      'appearance_settings': '外观设置',
      'language_settings': '语言设置',
      'server_configuration': '服务器配置',
      'about_us': '关于我们',
      'help_center': '帮助中心',
      'feedback': '意见反馈',
      'logout': '退出登录',
      'confirm_logout': '确认退出',
      'logout_message': '您确定要退出登录吗？',
      'logout_confirm': '确认退出',
      'clearing_user_data': '正在清除用户数据...',
      'user_data_cleared': '用户数据已清除完成',
      'logout_error': '退出登录时发生错误',

      // 无障碍性标签
      'page_navigation': '页面',
      'refreshing': '正在刷新',
      'refresh_page_content': '刷新页面内容',
      'news_item_label': '资讯',
      'app_item_label': '应用',
      'workspace_item_label': '工作台应用',
      'message_item_label': '消息',
      'loading_content': '正在加载内容',
      'content_loaded': '内容已加载',
      'home_page': '首页',
      'apps_page': '应用中心',
      'workspace_page': '工作台',
      'messages_page': '消息中心',
      'settings_page': '设置',

      // 服务器配置
      'server_address': '服务器地址',
      'server_port': '服务器端口',
      'server_name': '服务器名称',
      'scan_qr_code': '扫描配置二维码',
      'saved_servers': '已保存的服务器配置',
      'save_config': '保存配置',
      'test_connection': '测试连接',
      'no_saved_servers': '暂无保存的服务器配置',
      'config_saved': '服务器配置已保存',
      'config_updated': '服务器配置已更新',
      'connection_test_success': '连接测试成功',
      'enter_server_name': '请输入服务器名称',
      'enter_server_address': '请输入服务器地址',
      'enter_server_port': '请输入端口号',
      'server_address_required': '请输入服务器地址',
      'server_port_required': '请输入端口号',
      'confirm_delete': '确认删除',
      'confirm_delete_server': '确定要删除服务器',
      'use_server': '使用',
      'select_server': '选择',
      'edit_server_tooltip': '编辑',
      'delete_server_tooltip': '删除',
      'use_server_tooltip': '使用',

      // 二维码扫描
      'qr_scanner': '扫描二维码',
      'scan_instruction': '将二维码放入框内进行扫描',
      'scan_result': '扫描结果',
      'continue_scan': '继续扫描',
      'saved_servers_title': '已保存的服务器',
      'qr_scan_simulation': '模拟二维码扫描',
      'qr_input_hint': '请输入二维码内容（格式：ip:端口,名称）',
      'qr_example': '例如：*************:8080,测试服务器',
      'delete_server_message': '确定要删除服务器',
      'edit_server_config': '编辑服务器配置',
      'server_config_updated': '服务器配置已更新',
      'manual_input': '手动输入',
      'scan_success': '扫描成功',
      'use_result': '使用结果',
      'camera_error': '相机错误',
      'camera_permission_required': '需要相机权限',
      'camera_permission_hint': '请允许访问相机以扫描二维码',

      // 主题和语言
      'light_theme': '浅色',
      'dark_theme': '深色',
      'chinese': '中文',
      'english': 'English',
      'select_language': '选择语言',
      'select_theme': '选择主题',
      'language_updated': '语言设置已更新',
      'theme_updated': '主题设置已更新',
      'feature_under_development': '功能开发中...',

      // 新增翻译键
      'version_text': '版本',
      'light': '浅色',
      'dark': '深色',
      'manager_zhang': '张经理',
      'product_manager': '产品部 · 产品经理',
      'connection_test_success_msg': '连接测试成功',
      'qr_parse_success': '二维码解析成功',
      'qr_format_error': '二维码格式错误，请使用格式：ip:端口,名称',
      'server_name_example': '例如: 测试服务器',
      'question_mark': '吗？',
      'save_tooltip': '保存',
      'edit_tooltip': '编辑',
      'collapse_all': '全部折叠',
      'expand_all': '全部展开',
      'favorite_apps_saved': '常用应用设置已保存',
      'edit_mode_hint': '勾选则为常用应用',
      'workspace_edit_hint': '点击应用可调整排序',
      'refresh_success': '刷新成功',
      'refresh_failed': '刷新失败',
      'fetch_success': '获取成功',
      'image_load_failed': '图片加载失败',
      'fetch_success_cache': '获取成功（缓存）',
      'fetch_success_offline_cache': '获取成功（离线缓存）',
      'network_connection_failed': '网络连接失败，请检查网络设置',
      'no_messages_of_type': '暂无{type}消息',
      'enterprise_office_system': '企业办公系统',
      'efficient_collaboration': '高效协作，智能办公',
      'tech_company': '某某科技有限公司',
      'copyright_text': '© 2024 某某科技有限公司',
      'network_connection_error': '网络连接失败',
      'request_timeout': '请求超时',
      'server_error_msg': '服务器错误',
      'request_too_frequent': '请求过于频繁',
      'client_error': '请求错误',
      'unknown_error': '未知错误',
      'json_parse_error': '无法解析JSON响应',
      'token_expired': '登录已过期，请重新登录',
      'login_expired': '登录已过期',
      'token_expired_message': '您的登录状态已过期，为了保护您的账户安全，请重新登录。',
      'go_to_login': '去登录',
      'connection_test_failed': '连接测试失败',
      'connection_error': '连接错误',
      'connection_failed_title': '无法连接到服务器',
      'connection_failed_description': '很抱歉，无法连接到指定的服务器。请检查服务器地址和网络连接。',
      'target_server': '目标服务器',
      'connection_error_details': '错误详情',
      'suggestions': '建议解决方案',
      'connection_suggestions': '检查服务器地址和端口是否正确\n确认服务器是否正在运行\n检查网络连接是否正常\n确认防火墙设置是否允许连接',
      'back_to_config': '返回配置',
      'server_name_required': '服务器名称不能为空',
      'page_not_found': '页面未找到',
      'page_load_failed': '页面加载失败',
      'page_load_failed_description': '抱歉，请求的页面无法正常加载。可能是网络问题或页面不存在。',
      'error_info': '错误信息',
      'go_back': '返回',
      'retry': '重试',
      'help_tips': '帮助提示',
      'page_load_help_tips': '请检查网络连接是否正常，或稍后再试。如果问题持续存在，请联系技术支持。',
      'webview_load_failed': '页面内容加载失败，可能是网络问题或页面不存在。',
      'network_request_failed': '网络请求失败',
      'request_too_frequent_retry': '请求过于频繁，请稍后再试',
      'save_success': '保存成功',
      'sort_save_success': '排序保存成功',
      'fetch_failed': '获取失败',
      'save_failed': '保存失败',
      'connection_failed': '连接失败',

      // WebView相关
      'loading_failed': '加载失败',
      'loading_local_file_failed': '加载本地文件失败',
      'refreshing_page': '正在刷新页面...',
      'update_loading_progress': '更新加载进度',
      'judge_local_html_file': '判断是否为本地HTML文件',
      'reload_html_content_for_local_file': '对于本地文件，重新加载HTML内容',
      'use_standard_reload_for_network_url': '对于网络URL，使用标准的reload方法',
      'show_refresh_hint': '显示刷新提示',

      // 应用名称
      'schedule_management': '日程管理',
      'task_collaboration': '任务协作',
      'document_center': '文档中心',
      'customer_management': '客户管理',
      'project_management': '项目管理',
      'financial_reports': '财务报表',
      'expense_reimbursement': '费用报销',
      'email_system': '邮件系统',
      'video_conference': '视频会议',
      'budget_management': '预算管理',
      'invoice_system': '发票系统',
      'employee_management': '员工管理',
      'attendance_system': '考勤系统',
      'performance_review': '绩效评估',
      'training_center': '培训中心',
      'sales_tracking': '销售跟踪',
      'inventory_management': '库存管理',
      'instant_messaging': '即时通讯',
      'announcement_board': '公告板',
      'forum_discussion': '论坛讨论',
      'file_manager': '文件管理',
      'calculator': '计算器',
      'note_taking': '笔记记录',

      // 消息标题
      'system_maintenance_notice': '系统维护通知',
      'new_version_release': '新版本发布',
      'meeting_reminder': '会议提醒',
      'task_assignment': '任务分配',
      'approval_notification': '审批通知',

      // 分类标题
      'office_applications': '办公应用',
      'financial_applications': '财务应用',
      'hr_applications': '人力资源',
      'business_applications': '业务应用',
      'communication_applications': '沟通协作',
      'tool_applications': '工具应用',

      // 消息类型
      'all': '全部',
      'unread': '未读',
      'system': '系统',
      'work': '工作',

      // 示例消息数据
      'system_notification': '系统通知',
      'system_update_content': '您有一个新的系统更新，请及时查看',
      'system_maintenance_content': '系统将于本周末进行维护升级，请提前保存工作内容',
      'approval_reminder': '审批提醒',
      'approval_content': '您有2个待审批事项需要处理',
      'approval_pending': '审批待处理',
      'approval_pending_content': '您有3个审批流程待处理，请及时查看',
      'team_message': '团队消息',
      'team_content': '产品部：周会会议纪要已上传',
      'team_meeting_reminder': '团队会议提醒',
      'team_meeting_content': '明天下午2点有团队周会，请准时参加',
      'schedule_reminder': '日程提醒',
      'schedule_content': '明天10:00有产品评审会议',
      'schedule_update': '日程安排更新',
      'schedule_update_content': '您的明日日程已更新，请查看最新安排',
      'urgent_notice': '紧急通知',
      'urgent_content': '服务器维护通知：本周六凌晨2:00-4:00系统将进行例行维护',
      'security_alert': '安全提醒',
      'security_alert_content': '检测到异常登录，请确认是否为本人操作',
      'yesterday': '昨天',
      'days_ago_2': '2天前',
      'days_ago_3': '3天前',

      // 关于页面
      'app_description': '一款专为企业打造的移动办公平台，提供高效的工作协作和管理功能。',
      'common_questions': '常见问题',
      'help_question_1': '• 如何配置服务器？',
      'help_question_2': '• 如何扫描二维码？',
      'help_question_3': '• 如何切换语言？',
      'help_question_4': '• 如何联系技术支持？',
      'technical_support': '技术支持',

      // 网络测试页面
      'network_test_title': '网络错误处理测试',
      'network_test_subtitle': '测试网络错误处理和缓存机制',

      // API调用日志页面
      'api_call_log_title': 'API调用日志',
      'api_call_log_subtitle': '查看所有页面的API调用记录和调试信息',
      'api_request': 'API请求',
      'api_response': 'API响应',
      'request_method': '请求方法',
      'request_url': '请求地址',
      'request_headers': '请求头',
      'request_body': '请求体',
      'response_status': '响应状态',
      'response_data': '响应数据',
      'status_code': '状态码',
      'detailed_info': '详细信息',
      'api_call_time': '调用时间',
      'no_api_logs': '暂无API调用日志',
      'api_logs_cleared': 'API日志已清除',
      'copy_api_log': '复制API日志',
      'view_api_log': '查看API日志',
      'clear_api_logs': '清除API日志',
      'api_log_copied': 'API日志已复制到剪贴板',
      'test_logs_created': '已创建测试API日志',
      'create_test_logs': '创建测试日志',
      'expand': '展开',
      'collapse': '收起',
      'network_status': '网络状态',
      'connection_status': '连接状态',
      'connection_type': '连接类型',
      'metered_network': '计费网络',
      'last_check': '最后检查',
      'connected': '已连接',
      'disconnected': '已断开',
      'mobile_network': '移动网络',
      'wifi_network': 'WiFi网络',
      'ethernet_network': '以太网',
      'unknown_network': '未知网络',
      'yes': '是',
      'no': '否',
      'network_status_unknown': '网络状态未知',
      'api_test': 'API测试',
      'home_cache': '首页(缓存)',
      'home_refresh': '首页(刷新)',
      'messages_cache': '消息(缓存)',
      'messages_refresh': '消息(刷新)',
      'apps_cache': '应用(缓存)',
      'apps_refresh': '应用(刷新)',
      'test_results': '测试结果',
      'click_button_to_test': '点击上方按钮开始测试',
      'error_stats': '错误统计',
      'no_error_records': '暂无错误记录',
      'cache_control': '缓存控制',
      'clear_all_cache': '清除所有缓存',
      'clear_expired_cache': '清除过期缓存',
      'unknown_test_type': '未知测试类型',
      'status': '状态',
      'message': '消息',
      'source': '来源',
      'cache': '缓存',
      'network': '网络',
      'data_items': '数据项',
      'times': '次',

      // 错误日志相关
      'error_logs': '错误日志',
      'error_log_title': '错误日志',
      'error_log_subtitle': '查看应用错误记录',
      'no_error_logs': '暂无错误记录',
      'error_type': '错误类型',
      'error_message': '错误信息',
      'error_time': '发生时间',
      'error_details': '错误详情',
      'stack_trace': '堆栈跟踪',
      'copy_error': '复制错误信息',
      'copy_success': '已复制到剪贴板',
      'copy_filtered_success': '已复制过滤后的日志',
      'clear_all_logs': '清除所有日志',
      'clear_logs_confirm': '确定要清除所有错误日志吗？',
      'logs_cleared': '错误日志已清除',
      'error_count': '错误数量',
      'view_details': '查看详情',
      'close_details': '关闭详情',
      'flutter_error': 'Flutter错误',
      'dart_error': 'Dart错误',
      'log_network_error': '网络错误',
      'log_unknown_error': '未知错误',
      'app_error': '应用错误',

      // 时间相关
      'just_now': '刚刚',
      'minutes_ago': '分钟前',
      'hours_ago': '小时前',
      'days_ago': '天前',

      // 测试错误相关
      'test_flutter_error': '这是一个测试Flutter错误',
      'test_dart_error': '这是一个测试Dart错误',
      'test_network_error': '这是一个测试网络错误',
      'test_app_error': '这是一个测试应用错误',
      'test_error_created': '已创建测试错误',
      'create_test_error': '创建测试错误',

      // 调试信息
      'debug_force_refresh': '强制刷新',
      'debug_home_api_mock': '=== 首页信息接口 - 模拟模式 ===',
      'debug_apps_api_mock': '=== 应用列表接口 - 模拟模式 ===',
      'debug_messages_api_mock': '=== 消息列表接口 - 模拟模式 ===',
      'debug_check_workspace_cache': '检查工作台应用缓存...',
      'debug_found_workspace_cache': '找到工作台应用缓存数据，直接返回',
      'debug_not_found_workspace_cache': '未找到工作台应用缓存数据，继续调用API',
      'debug_force_refresh_skip_cache': '强制刷新，跳过缓存检查',
      'debug_error_service_init': '初始化完成，当前日志数量',
      'debug_error_service_init_failed': '初始化失败',
      'debug_error_logged': '记录错误',
      'debug_error_log_failed': '记录错误失败',
      'debug_all_logs_cleared': '已清除所有日志',
      'debug_clear_logs_failed': '清除日志失败',
      'debug_logs_cleared_by_type': '已清除类型为',
      'debug_clear_logs_by_type_failed': '清除指定类型日志失败',
      'debug_load_logs_failed': '加载日志失败',
      'debug_save_logs_failed': '保存日志失败',
      'debug_cleanup_old_logs': '清理了',
      'debug_cleanup_old_logs_suffix': '条过期日志',
      'debug_cleanup_logs_failed': '清理过期日志失败',
      'debug_save_logs_failed_simple': '保存日志失败',
      'debug_load_logs_failed_simple': '加载日志失败',
      'debug_check_login_status_failed': '检查登录状态失败',
      'debug_clear_login_info_failed': '清除登录信息失败',
      'debug_clear_user_data_failed': '清除用户数据失败',
      'debug_all_user_data_cleared': '所有用户数据清除完成，已设置强制刷新标记',
      'debug_force_refresh_flag_cleared': '强制刷新标记已清除',
      'debug_clear_force_refresh_failed': '清除强制刷新标记失败',
      'debug_update_session_failed': '更新会话状态失败',
      'debug_app_init_skip': '服务已初始化，跳过',
      'debug_app_init_start': '开始初始化应用服务...',
      'debug_init_network_status': '初始化网络状态服务',
      'debug_init_error_handler': '初始化网络错误处理器',
      'debug_init_http_client': '初始化HTTP客户端',
      'debug_init_api_service': '初始化API服务',
      'debug_cleanup_cache': '清理过期缓存',
      'debug_all_services_init': '所有服务初始化完成',
      'debug_init_failed': '初始化失败',
      'debug_dispose_services': '销毁所有服务',
      'debug_all_services_disposed': '所有服务已销毁',
      'debug_dispose_error': '销毁服务时出错',
      'debug_network_status_changed': '网络状态变化',
      'debug_network_type_detect_failed': '网络类型检测失败',

      // 过滤相关
      'filter_by': '过滤条件',
      'show_all': '显示全部',
      'no_logs_found': '未找到相关日志',

      // 调试日志管理
      'debug_log_management': '调试日志管理',
      'log_status': '日志状态',
      'log_enabled': '已开启',
      'log_disabled': '已关闭',
      'total_logs': '总日志数',
      'view_logs': '查看日志',
      'copy_logs': '复制日志',
      'clear_logs': '清除日志',
      'debug_logs_title': '调试日志',
      'logs_copied_success': '日志已复制到剪贴板',
      'confirm_clear_logs': '确认清除',
      'clear_logs_message': '确定要清除所有调试日志吗？此操作不可撤销。',
      'debug_logs_cleared': '调试日志已清除',

      // 免责声明
      'disclaimer_title': '免责声明',
      'disclaimer_content': '本软件仅为应用程序外壳，不包含任何实际业务内容。所有数据均来自第三方API接口，请勿用于非法用途。本软件不对数据的准确性、完整性或可靠性承担任何责任。使用本软件即表示您同意自行承担使用风险。',

      // 数据验证错误提示
      'app_data_invalid': '应用数据无效',
      'banner_data_invalid': '轮播图数据无效',
      'news_data_invalid': '新闻数据无效',
      'message_data_invalid': '消息数据无效',
      'no_apps_available': '暂无应用',
      'pull_to_refresh': '下拉刷新获取最新数据',
      'unknown_app': '未知应用',
      'unknown_category': '未知分类',
      'unknown_message': '未知消息',
      'api_call_failed': '接口调用失败',
      'load_failed': '加载失败',

      // WebView 404测试页面
      'test_webview_404': '测试WebView 404',
      'test_webview_404_description': '测试不同类型的WebView加载错误，验证404页面显示效果。',
      'test_invalid_url': '测试无效URL',
      'test_invalid_url_desc': '测试格式错误的URL',
      'test_nonexistent_domain': '测试不存在域名',
      'test_nonexistent_domain_desc': '测试无法解析的域名',
      'test_timeout': '测试连接超时',
      'test_timeout_desc': '测试30秒超时机制',
      'test_404_page': '测试404页面',
      'test_404_page_desc': '测试服务器返回404错误',
      'test_webview_note': '注意：iOS和Android的WebView错误处理机制可能不同，现已添加30秒超时机制。',

    },
    'en': {
      // 通用
      'app_name': 'Enterprise Mobile Office',
      'confirm': 'Confirm',
      'cancel': 'Cancel',
      'save': 'Save',
      'edit': 'Edit',
      'delete': 'Delete',
      'loading': 'Loading...',
      'success': 'Success',
      'error': 'Error',
      'warning': 'Warning',
      'info': 'Info',
      'ok': 'OK',
      'close': 'Close',
      'back': 'Back',
      'next': 'Next',
      'previous': 'Previous',
      'refresh': 'Refresh',
      'search': 'Search',
      'clear': 'Clear',
      'select_all': 'Select All',
      'deselect_all': 'Deselect All',
      'copy': 'Copy',
      'copy_all': 'Copy All',
      'log_statistics': 'Log Statistics',

      // 登录页面
      'login': 'Login',
      'username': 'Username',
      'password': 'Password',
      'remember_password': 'Remember Password',
      'login_button': 'Login',
      'server_config': 'Server Config',
      'login_failed': 'Login Failed',
      'invalid_credentials': 'Invalid username or password',
      'network_error': 'Network connection error',
      'server_error': 'Server error',
      'login_success': 'Login successful',
      'enter_username': 'Please enter username',
      'enter_password': 'Please enter password',
      'username_required': 'Please enter username',
      'password_required': 'Please enter password',
      'login_network_error': 'Login failed, please check network connection',
      'current_server': 'Current Server',
      'edit_server': 'Edit',
      'copyright': 'Copyright © 2023 Enterprise Mobile Office System',
      'version': 'Version',
      'build_time': 'Build Time',
      'test_login_persistence': 'Test Login Persistence',
      'test_started': 'Starting login persistence test...',
      'test_completed': 'Test completed, please check console output',
      'use_mock_login': 'Use Mock Login (Check to use mock data, uncheck to use real API)',

      // 主页面
      'home': 'Home',
      'apps': 'Apps',
      'workspace': 'Workspace',
      'messages': 'Messages',
      'settings': 'Settings',
      'my': 'My',

      // 首页
      'welcome': 'Welcome',
      'good_morning': 'Good Morning',
      'good_afternoon': 'Good Afternoon',
      'good_evening': 'Good Evening',
      'latest_news': 'Latest News',
      'company_news': 'Company News',
      'quick_actions': 'Quick Actions',
      'recent_apps': 'Recent Apps',
      'view_all': 'View All',
      'view_more': 'View More',
      'no_data': 'No Data',
      'no_news': 'No News',
      'loading_news': 'Loading news...',

      // 工作台
      'frequent_apps': 'Frequent Apps',
      'no_frequent_apps': 'No Frequent Apps',
      'no_frequent_apps_hint': 'Please contact administrator to configure frequent apps',
      'search_apps': 'Search Apps',
      'search_messages': 'Search Messages',
      'app_categories': 'App Categories',
      'all_apps': 'All Apps',
      'office_apps': 'Office Apps',
      'communication_apps': 'Communication Apps',
      'business_apps': 'Business Apps',
      'tool_apps': 'Tool Apps',
      'drag_to_sort': 'Drag to Sort',
      'add_to_workspace': 'Add to Workspace',
      'remove_from_workspace': 'Remove from Workspace',
      'edit_mode': 'Edit Mode',
      'save_changes': 'Save Changes',
      'changes_saved': 'Changes Saved',
      'sort_apps': 'Sort Apps',
      'drag_to_reorder': 'Drag to Reorder',
      'adjust_app_order': 'Adjust App Order',
      'select_new_position': 'Select new position:',
      'move_forward': 'Move Forward',
      'move_backward': 'Move Backward',
      'app_position_adjusted': 'App position adjusted',
      'already_first_last': 'Already first/last app',
      'moved_forward_to_position': 'moved forward to position',
      'moved_backward_to_position': 'moved backward to position',
      'app_not_found': 'App not found',

      // 消息
      'all_messages': 'All',
      'unread_messages': 'Unread',
      'system_messages': 'System',
      'work_messages': 'Work',
      'mark_as_read': 'Mark as Read',
      'reply': 'Reply',
      'forward': 'Forward',
      'message_details': 'Message Details',
      'no_messages': 'No Messages',
      'new_message': 'New Message',
      'send_message': 'Send Message',

      // 设置
      'account_settings': 'Account Settings',
      'enterprise_settings': 'Enterprise Settings',
      'app_preferences': 'App Preferences',
      'about_help': 'About & Help',
      'account_security': 'Account Security',
      'message_notifications': 'Message Notifications',
      'privacy_settings': 'Privacy Settings',
      'enterprise_info': 'Enterprise Info',
      'department_management': 'Department Management',
      'app_sorting': 'App Sorting',
      'appearance_settings': 'Appearance Settings',
      'language_settings': 'Language Settings',
      'server_configuration': 'Server Configuration',
      'about_us': 'About Us',
      'help_center': 'Help Center',
      'feedback': 'Feedback',
      'logout': 'Logout',
      'confirm_logout': 'Confirm Logout',
      'logout_message': 'Are you sure you want to logout?',
      'logout_confirm': 'Confirm Logout',
      'clearing_user_data': 'Clearing user data...',
      'user_data_cleared': 'User data cleared successfully',
      'logout_error': 'Error occurred during logout',

      // 无障碍性标签
      'page_navigation': 'Page',
      'refreshing': 'Refreshing',
      'refresh_page_content': 'Refresh page content',
      'news_item_label': 'News',
      'app_item_label': 'App',
      'workspace_item_label': 'Workspace app',
      'message_item_label': 'Message',
      'loading_content': 'Loading content',
      'content_loaded': 'Content loaded',
      'home_page': 'Home',
      'apps_page': 'Apps',
      'workspace_page': 'Workspace',
      'messages_page': 'Messages',
      'settings_page': 'Settings',

      // 服务器配置
      'server_address': 'Server Address',
      'server_port': 'Server Port',
      'server_name': 'Server Name',
      'scan_qr_code': 'Scan QR Code',
      'saved_servers': 'Saved Server Configurations',
      'save_config': 'Save Configuration',
      'test_connection': 'Test Connection',
      'no_saved_servers': 'No saved server configurations',
      'config_saved': 'Server configuration saved',
      'config_updated': 'Server configuration updated',
      'connection_test_success': 'Connection test successful',
      'enter_server_name': 'Enter server name',
      'enter_server_address': 'Enter server address',
      'enter_server_port': 'Enter port number',
      'server_address_required': 'Please enter server address',
      'server_port_required': 'Please enter port number',
      'confirm_delete': 'Confirm Delete',
      'confirm_delete_server': 'Are you sure you want to delete server',
      'use_server': 'Use',
      'select_server': 'Select',
      'edit_server_tooltip': 'Edit',
      'delete_server_tooltip': 'Delete',
      'use_server_tooltip': 'Use',

      // 二维码扫描
      'qr_scanner': 'QR Scanner',
      'scan_instruction': 'Place QR code within the frame to scan',
      'scan_result': 'Scan Result',
      'continue_scan': 'Continue Scanning',
      'saved_servers_title': 'Saved Servers',
      'qr_scan_simulation': 'QR Code Scan Simulation',
      'qr_input_hint': 'Enter QR code content (format: ip:port,name)',
      'qr_example': 'e.g: *************:8080,Test Server',
      'delete_server_message': 'Are you sure you want to delete server',
      'edit_server_config': 'Edit Server Configuration',
      'server_config_updated': 'Server configuration updated',
      'manual_input': 'Manual Input',
      'scan_success': 'Scan Success',
      'use_result': 'Use Result',
      'camera_error': 'Camera Error',
      'camera_permission_required': 'Camera Permission Required',
      'camera_permission_hint': 'Please allow camera access to scan QR codes',

      // 主题和语言
      'light_theme': 'Light',
      'dark_theme': 'Dark',
      'chinese': 'Chinese',
      'english': 'English',
      'select_language': 'Select Language',
      'select_theme': 'Select Theme',
      'language_updated': 'Language setting updated',
      'theme_updated': 'Theme setting updated',
      'feature_under_development': 'Feature under development...',

      // 新增翻译键
      'version_text': 'Version',
      'light': 'Light',
      'dark': 'Dark',
      'manager_zhang': 'Manager Zhang',
      'product_manager': 'Product Dept · Product Manager',
      'connection_test_success_msg': 'Connection test successful',
      'qr_parse_success': 'QR code parsed successfully',
      'qr_format_error':
          'QR code format error, please use format: ip:port,name',
      'server_name_example': 'e.g: Test Server',
      'question_mark': '?',
      'save_tooltip': 'Save',
      'edit_tooltip': 'Edit',
      'collapse_all': 'Collapse All',
      'expand_all': 'Expand All',
      'favorite_apps_saved': 'Favorite apps settings saved',
      'edit_mode_hint': 'Check to mark as favorite apps',
      'workspace_edit_hint': 'Tap apps to adjust order',
      'refresh_success': 'Refresh successful',
      'refresh_failed': 'Refresh failed',
      'fetch_success': 'Fetch successful',
      'image_load_failed': 'Image load failed',
      'fetch_success_cache': 'Fetch successful (Cache)',
      'fetch_success_offline_cache': 'Fetch successful (Offline Cache)',
      'network_connection_failed': 'Network connection failed, please check network settings',
      'no_messages_of_type': 'No {type} messages',
      'enterprise_office_system': 'Enterprise Office System',
      'efficient_collaboration': 'Efficient Collaboration, Smart Office',
      'tech_company': 'Some Technology Co., Ltd.',
      'copyright_text': '© 2024 Some Technology Co., Ltd.',
      'network_connection_error': 'Network connection failed',
      'request_timeout': 'Request timeout',
      'server_error_msg': 'Server error',
      'request_too_frequent': 'Request too frequent',
      'client_error': 'Client error',
      'unknown_error': 'Unknown error',
      'json_parse_error': 'Unable to parse JSON response',
      'token_expired': 'Login expired, please login again',
      'login_expired': 'Login Expired',
      'token_expired_message': 'Your login session has expired. For your account security, please login again.',
      'go_to_login': 'Go to Login',
      'connection_test_failed': 'Connection test failed',
      'connection_error': 'Connection Error',
      'connection_failed_title': 'Unable to connect to server',
      'connection_failed_description': 'Sorry, unable to connect to the specified server. Please check the server address and network connection.',
      'target_server': 'Target Server',
      'connection_error_details': 'Error Details',
      'suggestions': 'Suggested Solutions',
      'connection_suggestions': 'Check if server address and port are correct\nConfirm if server is running\nCheck if network connection is normal\nConfirm firewall settings allow connection',
      'back_to_config': 'Back to Config',
      'server_name_required': 'Server name cannot be empty',
      'page_not_found': 'Page Not Found',
      'page_load_failed': 'Page Load Failed',
      'page_load_failed_description': 'Sorry, the requested page could not be loaded properly. It may be a network issue or the page does not exist.',
      'error_info': 'Error Information',
      'go_back': 'Go Back',
      'retry': 'Retry',
      'help_tips': 'Help Tips',
      'page_load_help_tips': 'Please check if the network connection is normal, or try again later. If the problem persists, please contact technical support.',
      'webview_load_failed': 'Page content failed to load, possibly due to network issues or the page does not exist.',
      'network_request_failed': 'Network request failed',
      'request_too_frequent_retry': 'Request too frequent, please try again later',
      'save_success': 'Save successful',
      'sort_save_success': 'Sort saved successfully',
      'fetch_failed': 'Fetch failed',
      'save_failed': 'Save failed',
      'connection_failed': 'Connection failed',

      // WebView相关
      'loading_failed': 'Loading failed',
      'loading_local_file_failed': 'Failed to load local file',
      'refreshing_page': 'Refreshing page...',
      'update_loading_progress': 'Update loading progress',
      'judge_local_html_file': 'Check if it is a local HTML file',
      'reload_html_content_for_local_file':
          'For local files, reload HTML content',
      'use_standard_reload_for_network_url':
          'For network URLs, use standard reload method',
      'show_refresh_hint': 'Show refresh hint',

      // 应用名称
      'schedule_management': 'Schedule Management',
      'task_collaboration': 'Task Collaboration',
      'document_center': 'Document Center',
      'customer_management': 'Customer Management',
      'project_management': 'Project Management',
      'financial_reports': 'Financial Reports',
      'expense_reimbursement': 'Expense Reimbursement',
      'email_system': 'Email System',
      'video_conference': 'Video Conference',
      'budget_management': 'Budget Management',
      'invoice_system': 'Invoice System',
      'employee_management': 'Employee Management',
      'attendance_system': 'Attendance System',
      'performance_review': 'Performance Review',
      'training_center': 'Training Center',
      'sales_tracking': 'Sales Tracking',
      'inventory_management': 'Inventory Management',
      'instant_messaging': 'Instant Messaging',
      'announcement_board': 'Announcement Board',
      'forum_discussion': 'Forum Discussion',
      'file_manager': 'File Manager',
      'calculator': 'Calculator',
      'note_taking': 'Note Taking',

      // 消息标题
      'system_maintenance_notice': 'System Maintenance Notice',
      'new_version_release': 'New Version Release',
      'meeting_reminder': 'Meeting Reminder',
      'task_assignment': 'Task Assignment',
      'approval_notification': 'Approval Notification',

      // 分类标题
      'office_applications': 'Office Applications',
      'financial_applications': 'Financial Applications',
      'hr_applications': 'HR Applications',
      'business_applications': 'Business Applications',
      'communication_applications': 'Communication',
      'tool_applications': 'Tool Applications',

      // 消息类型
      'all': 'All',
      'unread': 'Unread',
      'system': 'System',
      'work': 'Work',

      // 示例消息数据
      'system_notification': 'System Notification',
      'system_update_content':
          'You have a new system update, please check it in time',
      'system_maintenance_content': 'System will undergo maintenance upgrade this weekend, please save your work in advance',
      'approval_reminder': 'Approval Reminder',
      'approval_content': 'You have 2 pending approval items to process',
      'approval_pending': 'Approval Pending',
      'approval_pending_content': 'You have 3 approval processes pending, please check in time',
      'team_message': 'Team Message',
      'team_content': 'Product Dept: Weekly meeting minutes uploaded',
      'team_meeting_reminder': 'Team Meeting Reminder',
      'team_meeting_content': 'Team weekly meeting at 2 PM tomorrow, please attend on time',
      'schedule_reminder': 'Schedule Reminder',
      'schedule_content': 'Product review meeting at 10:00 tomorrow',
      'schedule_update': 'Schedule Update',
      'schedule_update_content': 'Your tomorrow schedule has been updated, please check the latest arrangement',
      'urgent_notice': 'Urgent Notice',
      'urgent_content':
          'Server maintenance notice: System maintenance from 2:00-4:00 AM this Saturday',
      'security_alert': 'Security Alert',
      'security_alert_content': 'Abnormal login detected, please confirm if this is your operation',
      'yesterday': 'Yesterday',
      'days_ago_2': '2 days ago',
      'days_ago_3': '3 days ago',

      // 关于页面
      'app_description':
          'A mobile office platform designed for enterprises, providing efficient work collaboration and management functions.',
      'common_questions': 'Common Questions',
      'help_question_1': '• How to configure server?',
      'help_question_2': '• How to scan QR code?',
      'help_question_3': '• How to switch language?',
      'help_question_4': '• How to contact technical support?',
      'technical_support': 'Technical Support',

      // 网络测试页面
      'network_test_title': 'Network Error Handling Test',
      'network_test_subtitle': 'Test network error handling and caching mechanisms',

      // API调用日志页面
      'api_call_log_title': 'API Call Logs',
      'api_call_log_subtitle': 'View API call records and debug information from all pages',
      'api_request': 'API Request',
      'api_response': 'API Response',
      'request_method': 'Request Method',
      'request_url': 'Request URL',
      'request_headers': 'Request Headers',
      'request_body': 'Request Body',
      'response_status': 'Response Status',
      'response_data': 'Response Data',
      'status_code': 'Status Code',
      'detailed_info': 'Detailed Information',
      'api_call_time': 'Call Time',
      'no_api_logs': 'No API call logs',
      'api_logs_cleared': 'API logs cleared',
      'copy_api_log': 'Copy API Log',
      'view_api_log': 'View API Log',
      'clear_api_logs': 'Clear API Logs',
      'api_log_copied': 'API log copied to clipboard',
      'test_logs_created': 'Test API logs created',
      'create_test_logs': 'Create Test Logs',
      'expand': 'Expand',
      'collapse': 'Collapse',
      'network_status': 'Network Status',
      'connection_status': 'Connection Status',
      'connection_type': 'Connection Type',
      'metered_network': 'Metered Network',
      'last_check': 'Last Check',
      'connected': 'Connected',
      'disconnected': 'Disconnected',
      'mobile_network': 'Mobile Network',
      'wifi_network': 'WiFi Network',
      'ethernet_network': 'Ethernet',
      'unknown_network': 'Unknown Network',
      'yes': 'Yes',
      'no': 'No',
      'network_status_unknown': 'Network status unknown',
      'api_test': 'API Test',
      'home_cache': 'Home (Cache)',
      'home_refresh': 'Home (Refresh)',
      'messages_cache': 'Messages (Cache)',
      'messages_refresh': 'Messages (Refresh)',
      'apps_cache': 'Apps (Cache)',
      'apps_refresh': 'Apps (Refresh)',
      'test_results': 'Test Results',
      'click_button_to_test': 'Click the button above to start testing',
      'error_stats': 'Error Statistics',
      'no_error_records': 'No error records',
      'cache_control': 'Cache Control',
      'clear_all_cache': 'Clear All Cache',
      'clear_expired_cache': 'Clear Expired Cache',
      'unknown_test_type': 'Unknown test type',
      'status': 'Status',
      'message': 'Message',
      'source': 'Source',
      'cache': 'Cache',
      'network': 'Network',
      'data_items': 'Data Items',
      'times': 'times',

      // 错误日志相关
      'error_logs': 'Error Logs',
      'error_log_title': 'Error Logs',
      'error_log_subtitle': 'View application error records',
      'no_error_logs': 'No error records',
      'error_type': 'Error Type',
      'error_message': 'Error Message',
      'error_time': 'Error Time',
      'error_details': 'Error Details',
      'stack_trace': 'Stack Trace',
      'copy_error': 'Copy Error Info',
      'copy_success': 'Copied to clipboard',
      'copy_filtered_success': 'Copied filtered logs',
      'clear_all_logs': 'Clear All Logs',
      'clear_logs_confirm': 'Are you sure you want to clear all error logs?',
      'logs_cleared': 'Error logs cleared',
      'error_count': 'Error Count',
      'view_details': 'View Details',
      'close_details': 'Close Details',
      'flutter_error': 'Flutter Error',
      'dart_error': 'Dart Error',
      'log_network_error': 'Network Error',
      'log_unknown_error': 'Unknown Error',
      'app_error': 'App Error',

      // 时间相关
      'just_now': 'Just now',
      'minutes_ago': 'minutes ago',
      'hours_ago': 'hours ago',
      'days_ago': 'days ago',

      // 测试错误相关
      'test_flutter_error': 'This is a test Flutter error',
      'test_dart_error': 'This is a test Dart error',
      'test_network_error': 'This is a test network error',
      'test_app_error': 'This is a test app error',
      'test_error_created': 'Test error created',
      'create_test_error': 'Create Test Error',

      // 调试信息
      'debug_force_refresh': 'Force refresh',
      'debug_home_api_mock': '=== Home API - Mock Mode ===',
      'debug_apps_api_mock': '=== Apps API - Mock Mode ===',
      'debug_messages_api_mock': '=== Messages API - Mock Mode ===',
      'debug_check_workspace_cache': 'Checking workspace cache...',
      'debug_found_workspace_cache': 'Found workspace cache data, returning directly',
      'debug_not_found_workspace_cache': 'Workspace cache not found, calling API',
      'debug_force_refresh_skip_cache': 'Force refresh, skipping cache check',
      'debug_error_service_init': 'Initialization completed, current log count',
      'debug_error_service_init_failed': 'Initialization failed',
      'debug_error_logged': 'Error logged',
      'debug_error_log_failed': 'Failed to log error',
      'debug_all_logs_cleared': 'All logs cleared',
      'debug_clear_logs_failed': 'Failed to clear logs',
      'debug_logs_cleared_by_type': 'Cleared logs of type',
      'debug_clear_logs_by_type_failed': 'Failed to clear logs by type',
      'debug_load_logs_failed': 'Failed to load logs',
      'debug_save_logs_failed': 'Failed to save logs',
      'debug_cleanup_old_logs': 'Cleaned up',
      'debug_cleanup_old_logs_suffix': 'expired logs',
      'debug_cleanup_logs_failed': 'Failed to cleanup old logs',
      'debug_save_logs_failed_simple': 'Failed to save logs',
      'debug_load_logs_failed_simple': 'Failed to load logs',
      'debug_check_login_status_failed': 'Failed to check login status',
      'debug_clear_login_info_failed': 'Failed to clear login info',
      'debug_clear_user_data_failed': 'Failed to clear user data',
      'debug_all_user_data_cleared': 'All user data cleared, force refresh flag set',
      'debug_force_refresh_flag_cleared': 'Force refresh flag cleared',
      'debug_clear_force_refresh_failed': 'Failed to clear force refresh flag',
      'debug_update_session_failed': 'Failed to update session status',
      'debug_app_init_skip': 'Services already initialized, skipping',
      'debug_app_init_start': 'Starting application services initialization...',
      'debug_init_network_status': 'Initializing network status service',
      'debug_init_error_handler': 'Initializing network error handler',
      'debug_init_http_client': 'Initializing HTTP client',
      'debug_init_api_service': 'Initializing API service',
      'debug_cleanup_cache': 'Cleaning up expired cache',
      'debug_all_services_init': 'All services initialized successfully',
      'debug_init_failed': 'Initialization failed',
      'debug_dispose_services': 'Disposing all services',
      'debug_all_services_disposed': 'All services disposed',
      'debug_dispose_error': 'Error disposing services',
      'debug_network_status_changed': 'Network status changed',
      'debug_network_type_detect_failed': 'Network type detection failed',

      // 过滤相关
      'filter_by': 'Filter by',
      'show_all': 'Show All',
      'no_logs_found': 'No logs found for',

      // 调试日志管理
      'debug_log_management': 'Debug Log Management',
      'log_status': 'Log Status',
      'log_enabled': 'Enabled',
      'log_disabled': 'Disabled',
      'total_logs': 'Total Logs',
      'view_logs': 'View Logs',
      'copy_logs': 'Copy Logs',
      'clear_logs': 'Clear Logs',
      'debug_logs_title': 'Debug Logs',
      'logs_copied_success': 'Logs copied to clipboard',
      'confirm_clear_logs': 'Confirm Clear',
      'clear_logs_message': 'Are you sure you want to clear all debug logs? This action cannot be undone.',
      'debug_logs_cleared': 'Debug logs cleared',

      // 错误信息
      'field_required': 'This field is required',
      'invalid_format': 'Invalid format',
      'operation_failed': 'Operation failed',
      'permission_denied': 'Permission denied',
      'file_not_found': 'File not found',
      'network_timeout': 'Network timeout',
      'feature_in_development': 'Feature in development...',
      'please_wait': 'Please wait...',
      'processing': 'Processing...',
      'completed': 'Completed',
      'failed': 'Failed',
      'connection_success': 'Connection successful',
      'qr_scan_success': 'QR code scanned successfully',
      'qr_scan_failed': 'QR code scan failed',
      'invalid_qr_code': 'Invalid QR code',

      // 免责声明
      'disclaimer_title': 'Disclaimer',
      'disclaimer_content': 'This software is only an application shell and does not contain any actual business content. All displayed data comes from third-party API interfaces. Please do not use it for illegal purposes. This software does not assume any responsibility for the accuracy, completeness, or reliability of the data. By using this software, you agree to assume the risks of use at your own discretion.',

      // 数据验证错误提示
      'app_data_invalid': 'App data invalid',
      'banner_data_invalid': 'Banner data invalid',
      'news_data_invalid': 'News data invalid',
      'message_data_invalid': 'Message data invalid',
      'no_apps_available': 'No apps available',
      'pull_to_refresh': 'Pull to refresh for latest data',
      'unknown_app': 'Unknown App',
      'unknown_category': 'Unknown Category',
      'unknown_message': 'Unknown Message',
      'api_call_failed': 'API call failed',
      'load_failed': 'Load failed',

      // WebView 404测试页面
      'test_webview_404': 'Test WebView 404',
      'test_webview_404_description': 'Test different types of WebView loading errors to verify 404 page display.',
      'test_invalid_url': 'Test Invalid URL',
      'test_invalid_url_desc': 'Test malformed URL',
      'test_nonexistent_domain': 'Test Nonexistent Domain',
      'test_nonexistent_domain_desc': 'Test unresolvable domain',
      'test_timeout': 'Test Connection Timeout',
      'test_timeout_desc': 'Test 30-second timeout mechanism',
      'test_404_page': 'Test 404 Page',
      'test_404_page_desc': 'Test server returns 404 error',
      'test_webview_note': 'Note: iOS and Android WebView error handling mechanisms may differ. A 30-second timeout mechanism has been added.',

    },
  };

  static String _currentLanguage = defaultLanguage;

  static String get currentLanguage => _currentLanguage;

  static Future<void> loadLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    _currentLanguage = prefs.getString(_languageKey) ?? defaultLanguage;
  }

  static Future<void> setLanguage(String languageCode) async {
    _currentLanguage = languageCode;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_languageKey, languageCode);
  }

  static String translate(String key) {
    return _localizedStrings[_currentLanguage]?[key] ??
        _localizedStrings[defaultLanguage]?[key] ??
        key;
  }

  static String t(String key) => translate(key);

  static Locale get currentLocale {
    switch (_currentLanguage) {
      case 'zh':
        return const Locale('zh', 'CN');
      case 'en':
        return const Locale('en', 'US');
      default:
        return const Locale('zh', 'CN');
    }
  }

  static List<Locale> get supportedLocales => [
    const Locale('zh', 'CN'),
    const Locale('en', 'US'),
  ];

  static String getLanguageDisplayName(String languageCode) {
    switch (languageCode) {
      case 'zh':
        return translate('chinese');
      case 'en':
        return translate('english');
      default:
        return translate('chinese');
    }
  }
}
