import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../plugins/real_plugin_manager.dart';

class WebViewBridgeService {
  static WebViewController? _controller;

  static Future<void> initialize(WebViewController controller) async {
    _controller = controller;
    // 初始化真实的插件管理器
    await RealPluginManager.instance.initialize();
    _setupJavaScriptChannels();
  }

  static void registerBridge(WebViewController controller) {
    initialize(controller).then((_) {
      // 延迟注入API，确保页面加载完成
      Future.delayed(const Duration(milliseconds: 500), () {
        injectNativeAPI();
      });
    });
  }

  static void _setupJavaScriptChannels() {
    if (_controller == null) return;

    _controller!.addJavaScriptChannel(
      'nativeMethodCall',
      onMessageReceived: (JavaScriptMessage message) async {
        try {
          final data = jsonDecode(message.message);
          final method = data['method'] as String;
          final params = data['params'] as Map<String, dynamic>? ?? {};
          final callbackId = data['callbackId'] as String;

          // 执行原生方法
          final result = await RealPluginManager.instance.executeMethod(method, params);

          // 回调 JavaScript
          _callbackToJS(callbackId, {'success': true, 'data': result});
        } catch (e) {
          debugPrint('WebView bridge error: $e');
          final data = jsonDecode(message.message);
          final callbackId = data['callbackId'] as String;
          _callbackToJS(callbackId, {
            'success': false,
            'error': {
              'code': 'BRIDGE_ERROR',
              'message': e.toString(),
            }
          });
        }
      },
    );
  }

  static void _callbackToJS(String callbackId, Map<String, dynamic> result) {
    if (_controller == null) return;

    final jsCode = '''
      if (window.NativeCallbacks && window.NativeCallbacks['$callbackId']) {
        window.NativeCallbacks['$callbackId'](${jsonEncode(result)});
        delete window.NativeCallbacks['$callbackId'];
      }
    ''';

    _controller!.runJavaScript(jsCode);
  }

  // 主动向 WebView 发送消息
  static void sendMessageToWeb(String event, Map<String, dynamic> data) {
    if (_controller == null) return;

    final jsCode = '''
      if (window.onNativeEvent) {
        window.onNativeEvent('$event', ${jsonEncode(data)});
      }
    ''';

    _controller!.runJavaScript(jsCode);
  }

  // 注入原生API到WebView
  static void injectNativeAPI() {
    if (_controller == null) return;

    _controller!.runJavaScript('''
      // 创建原生API对象
      window.NativeAPI = {};
      window.NativeCallbacks = {};

      // 通用调用函数
      function callNativeMethod(method, params = {}) {
        return new Promise((resolve, reject) => {
          const callbackId = 'callback_' + Date.now() + '_' + Math.random();

          window.NativeCallbacks[callbackId] = (result) => {
            if (result.success) {
              resolve(result.data);
            } else {
              const error = new Error(result.error.message || 'Unknown error');
              error.code = result.error.code;
              reject(error);
            }
          };

          const message = {
            method: method,
            params: params,
            callbackId: callbackId
          };

          try {
            nativeMethodCall.postMessage(JSON.stringify(message));
          } catch (e) {
            delete window.NativeCallbacks[callbackId];
            reject(new Error('Failed to call native method: ' + e.message));
          }
        });
      }

      // 创建各个功能模块的API
      window.NativeAPI.Camera = {
        takePhoto: (params) => callNativeMethod('camera.takePhoto', params),
        recordVideo: (params) => callNativeMethod('camera.recordVideo', params),
        pickFromGallery: (params) => callNativeMethod('camera.pickFromGallery', params),
        pickMultipleFromGallery: (params) => callNativeMethod('camera.pickMultipleFromGallery', params),
        compressImage: (params) => callNativeMethod('camera.compressImage', params),
        compressVideo: (params) => callNativeMethod('camera.compressVideo', params)
      };

      window.NativeAPI.QRCode = {
        scan: (params) => callNativeMethod('qrcode.scan', params),
        generate: (params) => callNativeMethod('qrcode.generate', params),
        scanMultiple: (params) => callNativeMethod('qrcode.scanMultiple', params)
      };

      window.NativeAPI.Bluetooth = {
        isEnabled: (params) => callNativeMethod('bluetooth.isEnabled', params),
        enable: (params) => callNativeMethod('bluetooth.enable', params),
        scanDevices: (params) => callNativeMethod('bluetooth.scanDevices', params),
        connect: (params) => callNativeMethod('bluetooth.connect', params),
        disconnect: (params) => callNativeMethod('bluetooth.disconnect', params),
        printText: (params) => callNativeMethod('bluetooth.printText', params),
        printImage: (params) => callNativeMethod('bluetooth.printImage', params),
        printQRCode: (params) => callNativeMethod('bluetooth.printQRCode', params),
        printReceipt: (params) => callNativeMethod('bluetooth.printReceipt', params),
        cutPaper: (params) => callNativeMethod('bluetooth.cutPaper', params),
        getConnectionStatus: (params) => callNativeMethod('bluetooth.getConnectionStatus', params)
      };

      window.NativeAPI.NFC = {
        isAvailable: (params) => callNativeMethod('nfc.isAvailable', params),
        startScan: (params) => callNativeMethod('nfc.startScan', params),
        stopScan: (params) => callNativeMethod('nfc.stopScan', params),
        readTag: (params) => callNativeMethod('nfc.readTag', params),
        writeTag: (params) => callNativeMethod('nfc.writeTag', params)
      };

      window.NativeAPI.Location = {
        getCurrentPosition: (params) => callNativeMethod('location.getCurrentPosition', params),
        watchPosition: (params) => callNativeMethod('location.watchPosition', params),
        clearWatch: (params) => callNativeMethod('location.clearWatch', params),
        geocode: (params) => callNativeMethod('location.geocode', params),
        reverseGeocode: (params) => callNativeMethod('location.reverseGeocode', params),
        distanceBetween: (params) => callNativeMethod('location.distanceBetween', params),
        bearingBetween: (params) => callNativeMethod('location.bearingBetween', params)
      };

      window.NativeAPI.Device = {
        getDeviceId: (params) => callNativeMethod('device.getDeviceId', params),
        getDeviceInfo: (params) => callNativeMethod('device.getDeviceInfo', params),
        getUserInfo: (params) => callNativeMethod('device.getUserInfo', params),
        setUserInfo: (params) => callNativeMethod('device.setUserInfo', params),
        getAppInfo: (params) => callNativeMethod('device.getAppInfo', params),
        isPhysicalDevice: (params) => callNativeMethod('device.isPhysicalDevice', params),
        generateUniqueId: (params) => callNativeMethod('device.generateUniqueId', params)
      };

      window.NativeAPI.System = {
        showToast: (params) => callNativeMethod('system.showToast', params),
        showAlert: (params) => callNativeMethod('system.showAlert', params),
        showConfirm: (params) => callNativeMethod('system.showConfirm', params),
        showPrompt: (params) => callNativeMethod('system.showPrompt', params),
        vibrate: (params) => callNativeMethod('system.vibrate', params),
        openUrl: (params) => callNativeMethod('system.openUrl', params),
        shareContent: (params) => callNativeMethod('system.shareContent', params),
        copyToClipboard: (params) => callNativeMethod('system.copyToClipboard', params),
        getFromClipboard: (params) => callNativeMethod('system.getFromClipboard', params),
        hapticFeedback: (params) => callNativeMethod('system.hapticFeedback', params)
      };

      window.NativeAPI.TitleBar = {
        setTitle: (params) => callNativeMethod('titleBar.setTitle', params),
        setRightButton: (params) => callNativeMethod('titleBar.setRightButton', params),
        hideRightButton: (params) => callNativeMethod('titleBar.hideRightButton', params),
        showBackButton: (params) => callNativeMethod('titleBar.showBackButton', params),
        hideBackButton: (params) => callNativeMethod('titleBar.hideBackButton', params),
        navigateToHistory: (params) => callNativeMethod('titleBar.navigateToHistory', params),
        navigateToSettings: (params) => callNativeMethod('titleBar.navigateToSettings', params)
      };

      console.log('NativeAPI initialized successfully');
    ''');
  }
}