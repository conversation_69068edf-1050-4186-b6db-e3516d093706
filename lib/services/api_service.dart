import 'http_client.dart';
import 'cache_service.dart';
import 'localization_service.dart';
import 'auth_service.dart';
import 'debug_log_service.dart';
import 'api_delay_manager.dart';
import 'network_error_handler.dart';
import 'network_status_service.dart';
import 'token_expiry_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';
import '../constants/app_constants.dart';

class ApiService {
  static const String baseUrl = 'http://localhost:8080/api';
  static final HttpClient _httpClient = HttpClient();
  static final DebugLogService _logger = DebugLogService();
  static bool _isInitialized = false;

  // 全局模拟数据设置键
  static const String _useMockDataKey = 'use_mock_data';

  /// 初始化API服务
  static Future<void> initialize() async {
    if (!_isInitialized) {
      await _httpClient.initialize();
      // 初始化网络状态服务
      final networkStatus = NetworkStatusService();
      await networkStatus.initialize();
      _isInitialized = true;
    }
  }

  /// 获取全局模拟数据设置
  static Future<bool> getUseMockData() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_useMockDataKey) ?? true; // 默认使用模拟数据
  }

  /// 设置全局模拟数据选项
  static Future<void> setUseMockData(bool useMockData) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_useMockDataKey, useMockData);
  }

  /// 处理API错误，特别是token过期错误
  static Future<Map<String, dynamic>> _handleApiError(dynamic error, {BuildContext? context}) async {
    _logger.error('API调用失败: $error', tag: 'API');

    // 检查是否为token过期错误
    if (error is TokenExpiredException ||
        (error is NetworkError && error.type == NetworkErrorType.tokenExpired)) {

      // 如果有context且context仍然mounted，显示友好的对话框
      if (context != null && context.mounted) {
        final tokenHandler = TokenExpiryHandler();
        await tokenHandler.handleTokenExpiry(context);
      }

      return {
        'success': false,
        'message': LocalizationService.t('token_expired'),
        'isTokenExpired': true,
      };
    }

    // 处理其他网络错误
    if (error is NetworkError) {
      return {
        'success': false,
        'message': error.message,
        'errorType': error.type.toString(),
      };
    }

    // 默认错误处理
    return {
      'success': false,
      'message': error.toString(),
    };
  }

  /// 获取认证头
  static Future<Map<String, String>> _getAuthHeaders() async {
    // 使用 AuthService 获取 token，确保使用正确的存储键
    final authService = AuthService();
    final token = await authService.getUserToken() ?? '';

    // 调试信息：打印 token 状态
    _logger.debug('获取认证头 - Token: ${token.isEmpty ? "空" : "已获取(${token.length}字符)"}', tag: 'ApiService');
    if (token.isNotEmpty) {
      _logger.debug('Token前10字符: ${token.substring(0, token.length > 10 ? 10 : token.length)}...', tag: 'ApiService');
    }

    return {
      'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
    };
  }

  /// 打印详细的请求信息
  static void _logRequest(String method, String url, Map<String, String>? headers, dynamic body) {
    _logger.logApiRequest(method, url, headers, body);
  }

  /// 打印详细的响应信息
  static void _logResponse(String url, int statusCode, dynamic responseData, {String? error}) {
    _logger.logApiResponse(url, statusCode, responseData, error: error);
  }

  /// 获取动态的baseUrl，基于当前选中的服务器配置
  static Future<String> getBaseUrl() async {
    final prefs = await SharedPreferences.getInstance();
    final serverAddress = prefs.getString(AppConstants.keyServerAddress) ?? 'localhost';
    final serverPort = prefs.getString(AppConstants.keyServerPort) ?? '8080';

    // 调试信息
    _logger.debug('服务器配置 - 地址: $serverAddress, 端口: $serverPort', tag: 'API');

    // 检查地址是否已经包含端口号
    String baseUrl;
    if (serverAddress.contains(':')) {
      // 地址已经包含端口号，直接使用
      baseUrl = 'http://$serverAddress/api';
      _logger.debug('地址已包含端口号，使用: $baseUrl', tag: 'API');
    } else {
      // 地址不包含端口号，添加端口号
      baseUrl = 'http://$serverAddress:$serverPort/api';
      _logger.debug('地址不包含端口号，构建: $baseUrl', tag: 'API');
    }

    return baseUrl;
  }

  // 登录接口（支持模拟和真实接口）
  static Future<Map<String, dynamic>> login(
    String username,
    String password, {
    bool? useMockData,
  }) async {
    await initialize();

    // 如果没有指定useMockData，则使用全局设置
    final shouldUseMockData = useMockData ?? await getUseMockData();

    _logger.info('=== 登录接口调用开始 ===', tag: 'LOGIN');
    _logger.info('传入的useMockData参数: $useMockData', tag: 'LOGIN');
    _logger.info('全局设置shouldUseMockData: $shouldUseMockData', tag: 'LOGIN');

    try {
      if (shouldUseMockData) {
        _logger.info('=== 登录接口 - 模拟模式 ===', tag: 'LOGIN');
        _logger.info('用户名: $username', tag: 'LOGIN');
        _logger.info('密码: ${password.replaceAll(RegExp(r'.'), '*')}', tag: 'LOGIN');

        // 模拟登录
        await ApiDelayManager.withMinDelay(
          Future.value(null),
          type: 'login',
        );

        // 模拟登录验证
        if (username == 'admin' && password == '123456') {
          final result = {
            'success': true,
            'message': LocalizationService.t('login_success'),
            'data': {
              'token': 'mock_token_12345',
              'user': {
                'id': 1,
                'username': username,
                'name': '管理员Andy',
                'avatar': '',
                'department': 'it信息技术部',
              },
            },
          };
          _logger.info('模拟登录成功: $result', tag: 'LOGIN');
          return result;
        } else {
          final result = {
            'success': false,
            'message': LocalizationService.t('invalid_credentials'),
            'data': null,
          };
          _logger.warning('模拟登录失败: $result', tag: 'LOGIN');
          return result;
        }
      } else {
        // 真实接口登录
        final baseUrl = await getBaseUrl();
        final url = '$baseUrl/auth/login';
        final requestBody = {
          'username': username,
          'password': password,
        };

        _logRequest('POST', url, null, requestBody);

        // 确保最小延迟，让用户看到加载状态
        final requestFuture = _httpClient.post(
          url,
          body: requestBody,
        );

        final response = await ApiDelayManager.withMinDelay(
          requestFuture,
          type: 'login',
        );

        _logResponse(url, response.statusCode, response.json);

        if (response.statusCode == 200) {
          final data = response.json;
          if (data['success'] == true) {
            final result = {
              'success': true,
              'message': data['message'] ?? LocalizationService.t('login_success'),
              'data': data['data'],
            };
            _logger.info('真实登录成功: $result', tag: 'LOGIN');
            return result;
          } else {
            final result = {
              'success': false,
              'message': data['message'] ?? LocalizationService.t('login_failed'),
              'data': null,
            };
            _logger.warning('真实登录失败: $result', tag: 'LOGIN');
            return result;
          }
        } else {
          final result = {
            'success': false,
            'message': '${LocalizationService.t('login_failed')}：HTTP ${response.statusCode}',
            'data': null,
          };
          _logger.error('真实登录HTTP错误: $result', tag: 'LOGIN');
          _logResponse(url, response.statusCode, null, error: 'HTTP ${response.statusCode}');
          return result;
        }
      }
    } catch (e) {
      return {
        'success': false,
        'message': '${LocalizationService.t('login_failed')}：${e.toString()}',
        'data': null,
      };
    }
  }

  // 获取首页信息
  static Future<Map<String, dynamic>> getHomeInfo({
    bool forceRefresh = false,
    bool? useMockData,
    BuildContext? context,
  }) async {
    await initialize();

    // 如果没有指定useMockData，则使用全局设置
    final shouldUseMockData = useMockData ?? await getUseMockData();

    try {
      // 检查网络状态
      final networkStatus = NetworkStatusService();
      await networkStatus.initialize(); // 确保网络状态服务已初始化
      final isNetworkAvailable = networkStatus.isConnected;

      // 如果不是强制刷新，先尝试从缓存获取
      if (!forceRefresh) {
        final cachedData = await CacheService.getHomeData();
        if (cachedData != null) {
          // 如果有缓存且网络可用，提示使用缓存
          // 如果网络不可用，提示离线缓存
          final message = isNetworkAvailable
              ? LocalizationService.t('fetch_success_cache')
              : LocalizationService.t('fetch_success_offline_cache');
          return {
            'success': true,
            'message': message,
            'data': cachedData,
            'fromCache': true,
            'isOffline': !isNetworkAvailable,
          };
        }
      }

      // 如果网络不可用且没有缓存，直接返回网络错误
      if (!isNetworkAvailable) {
        return {
          'success': false,
          'message': LocalizationService.t('network_connection_failed'),
          'data': null,
          'error': 'Network not available',
          'isOffline': true,
        };
      }

      if (shouldUseMockData) {
        _logger.info(LocalizationService.t('debug_home_api_mock'), tag: 'HOME');
        _logger.info('${LocalizationService.t('debug_force_refresh')}: $forceRefresh', tag: 'HOME');

        // 模拟网络请求
        await ApiDelayManager.withMinDelay(
          Future.value(null),
          type: 'data',
        );

      final result = {
        'success': true,
        'message': LocalizationService.t('fetch_success'),
        'data': {
        'banners': [
          {
            'id': 1,
            'title': '企业数字化转型',
            'image': 'https://picsum.photos/400/200?random=1',
            'url': '',
            'description': '了解最新的数字化转型趋势',
          },
          {
            'id': 2,
            'title': '新产品发布会',
            'image': 'https://picsum.photos/400/200?random=2',
            'url': '',
            'description': '体验我们的最新产品功能',
          },
          {
            'id': 3,
            'title': '团队建设活动',
            'image': 'https://picsum.photos/400/200?random=3',
           'url': '',
            'description': '加强团队协作与沟通',
          },
          {
            'id': 4,
            'title': '年度表彰大会',
            'image': 'https://picsum.photos/400/200?random=4',
            'url': '',
            'description': '表彰优秀员工和团队（本地详情页）',
          },
          {
            'id': 5,
            'title': '技术创新论坛',
            'image': 'https://picsum.photos/400/200?random=5',
           'url': '',
            'description': '探讨前沿技术发展趋势',
          },
        ].take(5).toList(), // 限制最多5条轮播图
        'news': [
          {
            'id': 1,
            'title': '公司Q4季度总结会议通知',
            'summary': '定于本月底召开Q4季度总结会议，请各部门做好准备...',
            'publishTime': '2024-12-20 10:00:00',
            'category': '公司公告',
          },
          {
            'id': 2,
            'title': '新员工入职培训安排',
            'summary': '为帮助新员工快速融入团队，特安排入职培训...',
            'publishTime': '2024-12-19 14:30:00',
            'category': '人事通知',
          },
          {
            'id': 3,
            'title': '系统维护通知',
            'summary': '为提升系统性能，将于本周末进行系统维护...',
            'publishTime': '2024-12-18 16:00:00',
            'category': '技术公告',
          },
          {
            'id': 4,
            'title': '年度优秀员工评选结果公布',
            'summary': '经过严格评选，现公布2024年度优秀员工名单...',
            'publishTime': '2024-12-17 09:00:00',
            'category': '人事通知',
          },
          {
            'id': 5,
            'title': '办公设备升级通知',
            'summary': '为提高工作效率，公司将对部分办公设备进行升级...',
            'publishTime': '2024-12-16 15:20:00',
            'category': '行政通知',
          },
          {
            'id': 6,
            'title': '春节放假安排通知',
            'summary': '根据国家法定节假日安排，现将春节放假时间通知如下...',
            'publishTime': '2024-12-15 11:30:00',
            'category': '公司公告',
          },
          {
            'id': 7,
            'title': '安全生产培训会议',
            'summary': '为加强安全生产意识，提高安全防范能力，特举办培训会议...',
            'publishTime': '2024-12-14 13:45:00',
            'category': '安全培训',
          },
          {
            'id': 8,
            'title': '客户满意度调研报告',
            'summary': '本季度客户满意度调研已完成，整体满意度达到95%...',
            'publishTime': '2024-12-13 16:10:00',
            'category': '业务报告',
          },
          {
            'id': 9,
            'title': '新产品研发进展更新',
            'summary': '智能办公系统2.0版本研发进展顺利，预计明年Q1发布...',
            'publishTime': '2024-12-12 10:25:00',
            'category': '产品动态',
          },
          {
            'id': 10,
            'title': '员工健康体检安排',
            'summary': '为关爱员工健康，公司安排年度健康体检，请按时参加...',
            'publishTime': '2024-12-11 14:00:00',
            'category': '员工福利',
          },
          {
            'id': 11,
            'title': '技术分享会成功举办',
            'summary': '本月技术分享会圆满结束，多位技术专家分享了前沿技术...',
            'publishTime': '2024-12-10 17:30:00',
            'category': '技术交流',
          },
          {
            'id': 12,
            'title': '环保倡议书',
            'summary': '为响应绿色办公理念，公司发起环保倡议，号召全员参与...',
            'publishTime': '2024-12-09 09:15:00',
            'category': '企业文化',
          },
          {
            'id': 13,
            'title': '财务报销流程优化通知',
            'summary': '为简化报销流程，提高效率，财务部门对报销流程进行优化...',
            'publishTime': '2024-12-08 11:40:00',
            'category': '流程优化',
          },
          {
            'id': 14,
            'title': '团建活动报名开始',
            'summary': '年底团建活动即将开始，精彩活动等你参与，快来报名吧...',
            'publishTime': '2024-12-07 08:50:00',
            'category': '员工活动',
          },
          {
            'id': 15,
            'title': '知识产权保护培训',
            'summary': '为提高全员知识产权保护意识，特举办专题培训讲座...',
            'publishTime': '2024-12-06 15:00:00',
            'category': '法律培训',
          },
        ].take(15).toList(), // 限制最多15条新闻
      },
    };

        // 保存到缓存
        final responseData = result['data'] as Map<String, dynamic>;
        await CacheService.saveHomeData(responseData);

        _logger.info('首页信息模拟数据返回: ${result.keys}', tag: 'HOME');
        _logger.info('轮播图数量: ${(responseData['banners'] as List).length}', tag: 'HOME');
        _logger.info('新闻数量: ${(responseData['news'] as List).length}', tag: 'HOME');
        return result;
      } else {
        // 真实接口请求
        final baseUrl = await getBaseUrl();
        final headers = await _getAuthHeaders();
        final url = '$baseUrl/home/<USER>';

        _logRequest('GET', url, headers, null);

        // 确保最小延迟，让用户看到加载状态
        final requestFuture = _httpClient.get(
          url,
          headers: headers,
        );

        final response = await ApiDelayManager.withMinDelay(
          requestFuture,
          type: 'data',
        );

        _logResponse(url, response.statusCode, response.json);

        if (response.statusCode == 200) {
          final data = response.json;
          _logger.info('首页接口响应数据结构: ${data.keys}', tag: 'HOME');

          if (data['success'] == true) {
            // 处理API返回的数据，确保即使数据为空也能正常处理
            final responseData = data['data'] ?? {};
            final processedData = _processHomeData(responseData);

            // 保存到缓存
            await CacheService.saveHomeData(processedData);

            final result = {
              'success': true,
              'message': data['message'] ?? LocalizationService.t('fetch_success'),
              'data': processedData,
              'fromCache': false,
            };
            _logger.info('首页接口成功: ${result.keys}', tag: 'HOME');
            return result;
          } else {
            _logger.warning('首页接口业务失败: ${data['message']}', tag: 'HOME');

            // 检查是否为token相关错误（后端可能返回200状态码但业务失败）
            final errorMessage = data['message'] ?? '';
            final tokenHandler = TokenExpiryHandler();
            if (tokenHandler.isTokenExpiredError(errorMessage)) {
              _logger.warning('检测到token过期错误（业务层）: $errorMessage', tag: 'HOME');

              // 手动触发token过期处理
              if (context != null && context.mounted) {
                await tokenHandler.handleTokenExpiry(context);
              }

              return {
                'success': false,
                'message': LocalizationService.t('token_expired'),
                'isTokenExpired': true,
              };
            }

            // 真实接口失败时返回空数据，不使用测试数据
            return {
              'success': false,
              'message': data['message'] ?? LocalizationService.t('fetch_failed'),
              'data': _getEmptyHomeData(),
              'error': data['message'] ?? LocalizationService.t('fetch_failed'),
            };
          }
        } else {
          _logger.error('首页接口HTTP错误: ${response.statusCode}', tag: 'HOME');
          // HTTP错误时返回空数据，不使用测试数据
          return {
            'success': false,
            'message': 'HTTP ${response.statusCode}',
            'data': _getEmptyHomeData(),
            'error': 'HTTP ${response.statusCode}',
          };
        }
      }
    } catch (e) {
      // 处理API错误，特别是token过期错误
      final errorResult = await _handleApiError(e, context: context);
      if (errorResult['isTokenExpired'] == true) {
        return errorResult;
      }

      // 网络请求失败，尝试从缓存获取
      final cachedData = await CacheService.getHomeData();
      if (cachedData != null) {
        return {
          'success': true,
          'message': LocalizationService.t('fetch_success_offline_cache'),
          'data': cachedData,
          'fromCache': true,
        };
      }

      // 没有缓存数据，返回错误
      return {
        'success': false,
        'message': LocalizationService.t('network_connection_failed'),
        'data': null,
        'error': e.toString(),
      };
    }
  }

  // 获取应用列表
  static Future<Map<String, dynamic>> getAppList({
    bool forceRefresh = false,
    bool? useMockData,
    BuildContext? context,
  }) async {
    await initialize();

    // 如果没有指定useMockData，则使用全局设置
    final shouldUseMockData = useMockData ?? await getUseMockData();

    try {
      // 检查网络状态
      final networkStatus = NetworkStatusService();
      await networkStatus.initialize(); // 确保网络状态服务已初始化
      final isNetworkAvailable = networkStatus.isConnected;

      // 如果不是强制刷新，先尝试从缓存获取
      if (!forceRefresh) {
        final cachedData = await CacheService.getAppsData();
        if (cachedData != null) {
          // 如果有缓存且网络可用，提示使用缓存
          // 如果网络不可用，提示离线缓存
          final message = isNetworkAvailable
              ? LocalizationService.t('fetch_success_cache')
              : LocalizationService.t('fetch_success_offline_cache');
          return {
            'success': true,
            'message': message,
            'data': cachedData,
            'fromCache': true,
            'isOffline': !isNetworkAvailable,
          };
        }
      }

      // 如果网络不可用且没有缓存，直接返回网络错误
      if (!isNetworkAvailable) {
        return {
          'success': false,
          'message': LocalizationService.t('network_connection_failed'),
          'data': null,
          'error': 'Network not available',
          'isOffline': true,
        };
      }

      if (shouldUseMockData) {
        _logger.info(LocalizationService.t('debug_apps_api_mock'), tag: 'APPS');
        _logger.info('${LocalizationService.t('debug_force_refresh')}: $forceRefresh', tag: 'APPS');

        await ApiDelayManager.withMinDelay(
          Future.value(null),
          type: 'data',
        );

      final result = {
        'success': true,
        'message': LocalizationService.t('fetch_success'),
        'data': {
        'categories': [
          {
            'id': 1,
            'title': '办公协作',
            'apps': [
              {
                'id': 1,
                'name': '日程管理',
                'icon': 'calendar_today',
                'color': '0xFF3366CC',
                'url': '', // 改为本地详情页
                'description': '管理个人和团队日程安排',
                'category': '办公协作',
                'sort': 1,
              },
              {
                'id': 2,
                'name': '任务协作',
                'icon': 'assignment',
                'color': '0xFFFF9900',
                'url': '', // 改为本地详情页
                'description': '团队任务分配和协作',
                'category': '办公协作',
                'sort': 2,
              },
              {
                'id': 3,
                'name': '文档中心',
                'icon': 'description',
                'color': '0xFF52C41A',
                'url': '', // 改为本地详情页
                'description': '企业文档管理和共享',
                'category': '办公协作',
                'sort': 3,
              },
            ],
          },
          {
            'id': 2,
            'title': '业务管理',
            'apps': [
              {
                'id': 4,
                'name': '客户管理',
                'icon': 'people',
                'color': '0xFF9C27B0',
                'url': '', // 改为本地详情页
                'description': '客户关系管理系统',
                'category': '业务管理',
                'sort': 1,
              },
              {
                'id': 5,
                'name': '销售统计',
                'icon': 'bar_chart',
                'color': '0xFF00BCD4',
                'url': '', // 本地详情页
                'description': '销售数据统计分析',
                'category': '业务管理',
                'sort': 2,
              },
            ],
          },
          {
            'id': 3,
            'title': '人事管理',
            'apps': [
              {
                'id': 6,
                'name': '考勤打卡',
                'icon': 'access_time',
                'color': '0xFFFF5722',
                'url': '', // 改为本地详情页
                'description': '员工考勤管理',
                'category': '人事管理',
                'sort': 1,
              },
              {
                'id': 7,
                'name': '请假申请',
                'icon': 'event_busy',
                'color': '0xFF795548',
                'url': '', // 本地详情页
                'description': '在线请假申请系统',
                'category': '人事管理',
                'sort': 2,
              },
            ],
          },
          {
            'id': 4,
            'title': '设备功能测试',
            'apps': [
              {
                'id': 8,
                'name': '相机功能',
                'icon': 'camera_alt',
                'color': '0xFF2196F3',
                'url': 'assets/html/webview_api_test.html#camera',
                'description': '测试拍照、录像、相册选择等相机功能',
                'category': '设备功能测试',
                'sort': 1,
              },
              {
                'id': 9,
                'name': '二维码扫描',
                'icon': 'qr_code_scanner',
                'color': '0xFF4CAF50',
                'url': 'assets/html/webview_api_test.html#qrcode',
                'description': '测试二维码扫描、生成、批量处理功能',
                'category': '设备功能测试',
                'sort': 2,
              },
              {
                'id': 10,
                'name': '蓝牙打印',
                'icon': 'bluetooth',
                'color': '0xFF9C27B0',
                'url': 'assets/html/webview_api_test.html#bluetooth',
                'description': '测试蓝牙设备连接和打印功能',
                'category': '设备功能测试',
                'sort': 3,
              },
              {
                'id': 11,
                'name': 'NFC功能',
                'icon': 'nfc',
                'color': '0xFFFF9800',
                'url': 'assets/html/webview_api_test.html#nfc',
                'description': '测试NFC标签读写和数据交换功能',
                'category': '设备功能测试',
                'sort': 4,
              },
              {
                'id': 12,
                'name': '定位服务',
                'icon': 'location_on',
                'color': '0xFFF44336',
                'url': 'assets/html/webview_api_test.html#location',
                'description': '测试位置获取和地址解析功能',
                'category': '设备功能测试',
                'sort': 5,
              },
              {
                'id': 13,
                'name': '设备信息',
                'icon': 'phone_android',
                'color': '0xFF607D8B',
                'url': 'assets/html/webview_api_test.html#device',
                'description': '测试设备标识和信息获取功能',
                'category': '设备功能测试',
                'sort': 6,
              },
              {
                'id': 14,
                'name': '系统功能',
                'icon': 'settings',
                'color': '0xFF795548',
                'url': 'assets/html/webview_api_test.html#system',
                'description': '测试提示框、震动、分享等系统功能',
                'category': '设备功能测试',
                'sort': 7,
              },
              {
                'id': 15,
                'name': '标题栏控制',
                'icon': 'title',
                'color': '0xFF3F51B5',
                'url': 'assets/html/webview_api_test.html#titlebar',
                'description': '测试标题栏动态控制功能',
                'category': '设备功能测试',
                'sort': 8,
              },
              {
                'id': 16,
                'name': '功能总览',
                'icon': 'dashboard',
                'color': '0xFF673AB7',
                'url': 'assets/html/webview_api_test.html',
                'description': '查看所有设备功能的完整测试页面',
                'category': '设备功能测试',
                'sort': 9,
              },
              {
                'id': 17,
                'name': '快速演示',
                'icon': 'play_circle_filled',
                'color': '0xFFE91E63',
                'url': 'assets/html/device_demo.html',
                'description': '快速体验主要设备功能的演示页面',
                'category': '设备功能测试',
                'sort': 10,
              },
               {
          'name': '权限测试',
          'icon': 'security',
          'color': '0xFF9C27B0',
          'url': 'assets/html/real_device_permissions_test.html',
          'description': '专门测试设备权限请求和授权状态',
        },
        {
          'name': '权限调试',
          'icon': 'bug_report',
          'color': '0xFFFF5722',
          'url': 'assets/html/permission_debug.html',
          'description': '诊断权限问题，提供详细的错误信息和解决方案',
        },
        {
          'name': '强制权限请求',
          'icon': 'flash_on',
          'color': '0xFFE91E63',
          'url': 'assets/html/force_permission_test.html',
          'description': '立即弹出系统权限对话框，强制请求所有权限',
        },
            ],
          },
        ],
      },
    };

        // 保存到缓存
        final responseData = result['data'] as Map<String, dynamic>;
        await CacheService.saveAppsData(responseData);

        _logger.info('应用列表模拟数据返回: ${result.keys}', tag: 'APPS');
        _logger.info('应用分类数量: ${(responseData['categories'] as List).length}', tag: 'APPS');
        return result;
      } else {
        // 真实接口请求
        final baseUrl = await getBaseUrl();
        final headers = await _getAuthHeaders();
        final url = '$baseUrl/apps/list';

        _logRequest('GET', url, headers, null);

        // 确保最小延迟，让用户看到加载状态
        final requestFuture = _httpClient.get(
          url,
          headers: headers,
        );

        final response = await ApiDelayManager.withMinDelay(
          requestFuture,
          type: 'data',
        );

        _logResponse(url, response.statusCode, response.json);

        if (response.statusCode == 200) {
          final data = response.json;
          _logger.info('应用列表接口响应数据结构: ${data.keys}', tag: 'APPS');

          if (data['success'] == true) {
            // 处理API返回的数据，确保即使数据为空也能正常处理
            final responseData = data['data'] ?? {};
            final processedData = _processAppsData(responseData);

            // 保存到缓存
            await CacheService.saveAppsData(processedData);

            final result = {
              'success': true,
              'message': data['message'] ?? LocalizationService.t('fetch_success'),
              'data': processedData,
              'fromCache': false,
            };
            _logger.info('应用列表接口成功: ${result.keys}', tag: 'APPS');
            return result;
          } else {
            _logger.warning('应用列表接口业务失败: ${data['message']}', tag: 'APPS');

            // 检查是否为token相关错误（后端可能返回200状态码但业务失败）
            final errorMessage = data['message'] ?? '';
            final tokenHandler = TokenExpiryHandler();
            if (tokenHandler.isTokenExpiredError(errorMessage)) {
              _logger.warning('检测到token过期错误（业务层）: $errorMessage', tag: 'APPS');

              // 手动触发token过期处理
              if (context != null && context.mounted) {
                await tokenHandler.handleTokenExpiry(context);
              }

              return {
                'success': false,
                'message': LocalizationService.t('token_expired'),
                'isTokenExpired': true,
              };
            }

            // 真实接口失败时返回空数据，不使用测试数据
            return {
              'success': false,
              'message': data['message'] ?? LocalizationService.t('fetch_failed'),
              'data': _getEmptyAppsData(),
              'error': data['message'] ?? LocalizationService.t('fetch_failed'),
            };
          }
        } else {
          _logger.error('应用列表接口HTTP错误: ${response.statusCode}', tag: 'APPS');
          // HTTP错误时返回空数据，不使用测试数据
          return {
            'success': false,
            'message': 'HTTP ${response.statusCode}',
            'data': _getEmptyAppsData(),
            'error': 'HTTP ${response.statusCode}',
          };
        }
      }
    } catch (e) {
      // 处理API错误，特别是token过期错误
      final errorResult = await _handleApiError(e, context: context);
      if (errorResult['isTokenExpired'] == true) {
        return errorResult;
      }

      // 网络请求失败，尝试从缓存获取
      final cachedData = await CacheService.getAppsData();
      if (cachedData != null) {
        return {
          'success': true,
          'message': LocalizationService.t('fetch_success_offline_cache'),
          'data': cachedData,
          'fromCache': true,
        };
      }

      // 没有缓存数据，返回错误
      return {
        'success': false,
        'message': LocalizationService.t('network_connection_failed'),
        'data': null,
        'error': e.toString(),
      };
    }
  }



  // 获取消息列表
  static Future<Map<String, dynamic>> getMessages({
    bool forceRefresh = false,
    bool? useMockData,
    BuildContext? context,
  }) async {
    await initialize();

    // 如果没有指定useMockData，则使用全局设置
    final shouldUseMockData = useMockData ?? await getUseMockData();

    try {
      // 检查网络状态
      final networkStatus = NetworkStatusService();
      await networkStatus.initialize(); // 确保网络状态服务已初始化
      final isNetworkAvailable = networkStatus.isConnected;

      // 如果不是强制刷新，先尝试从缓存获取
      if (!forceRefresh) {
        final cachedData = await CacheService.getMessagesData();
        if (cachedData != null) {
          // 如果有缓存且网络可用，提示使用缓存
          // 如果网络不可用，提示离线缓存
          final message = isNetworkAvailable
              ? LocalizationService.t('fetch_success_cache')
              : LocalizationService.t('fetch_success_offline_cache');
          return {
            'success': true,
            'message': message,
            'data': cachedData,
            'fromCache': true,
            'isOffline': !isNetworkAvailable,
          };
        }
      }

      // 如果网络不可用且没有缓存，直接返回网络错误
      if (!isNetworkAvailable) {
        return {
          'success': false,
          'message': LocalizationService.t('network_connection_failed'),
          'data': null,
          'error': 'Network not available',
          'isOffline': true,
        };
      }

      if (shouldUseMockData) {
        _logger.info(LocalizationService.t('debug_messages_api_mock'), tag: 'MESSAGES');
        _logger.info('${LocalizationService.t('debug_force_refresh')}: $forceRefresh', tag: 'MESSAGES');

        await ApiDelayManager.withMinDelay(
          Future.value(null),
          type: 'data',
        );

      final result = {
        'success': true,
        'message': LocalizationService.t('fetch_success'),
        'data': {
        'messages': [
          {
            'id': 1,
            'title': LocalizationService.t('system_maintenance_notice'),
            'content': LocalizationService.t('system_maintenance_content'),
            'time': '10:30',
            'icon': 'notifications',
            'color': '0xFF3366CC',
            'unreadCount': 1,
            'type': 'system',  // 使用固定的英文键值
          },
          {
            'id': 2,
            'title': LocalizationService.t('approval_pending'),
            'content': LocalizationService.t('approval_pending_content'),
            'time': '09:15',
            'icon': 'assignment_turned_in',
            'color': '0xFFFF9900',
            'unreadCount': 3,
            'type': 'work',  // 使用固定的英文键值
          },
          {
            'id': 3,
            'title': LocalizationService.t('team_meeting_reminder'),
            'content': LocalizationService.t('team_meeting_content'),
            'time': LocalizationService.t('yesterday'),
            'icon': 'group',
            'color': '0xFF52C41A',
            'unreadCount': 0,
            'type': 'work',  // 使用固定的英文键值
          },
          {
            'id': 4,
            'title': LocalizationService.t('schedule_update'),
            'content': LocalizationService.t('schedule_update_content'),
            'time': LocalizationService.t('yesterday'),
            'icon': 'event',
            'color': '0xFF9C27B0',
            'unreadCount': 0,
            'type': 'work',  // 使用固定的英文键值
          },
          {
            'id': 5,
            'title': LocalizationService.t('security_alert'),
            'content': LocalizationService.t('security_alert_content'),
            'time': LocalizationService.t('days_ago_2'),
            'icon': 'warning',
            'color': '0xFFFF5722',
            'unreadCount': 1,
            'type': 'system',  // 使用固定的英文键值
          },
        ],
      },
    };

        // 保存到缓存
        final responseData = result['data'] as Map<String, dynamic>;
        await CacheService.saveMessagesData(responseData);

        return result;
      } else {
        // 真实接口请求
        final baseUrl = await getBaseUrl();
        final url = '$baseUrl/messages/list';
        final headers = await _getAuthHeaders();

        // 确保最小延迟，让用户看到加载状态
        final requestFuture = _httpClient.get(url, headers: headers);

        final response = await ApiDelayManager.withMinDelay(
          requestFuture,
          type: 'data',
        );

        if (response.statusCode == 200) {
          final data = response.json;
          if (data['success'] == true) {
            // 处理API返回的数据，确保即使数据为空也能正常处理
            final responseData = data['data'] ?? {};
            final processedData = _processMessagesData(responseData);

            // 保存到缓存
            await CacheService.saveMessagesData(processedData);

            return {
              'success': true,
              'message': data['message'] ?? LocalizationService.t('fetch_success'),
              'data': processedData,
              'fromCache': false,
            };
          } else {
            // 检查是否为token相关错误（后端可能返回200状态码但业务失败）
            final errorMessage = data['message'] ?? '';
            final tokenHandler = TokenExpiryHandler();
            if (tokenHandler.isTokenExpiredError(errorMessage)) {
              _logger.warning('检测到token过期错误（业务层）: $errorMessage', tag: 'MESSAGES');

              // 手动触发token过期处理
              if (context != null && context.mounted) {
                await tokenHandler.handleTokenExpiry(context);
              }

              return {
                'success': false,
                'message': LocalizationService.t('token_expired'),
                'isTokenExpired': true,
              };
            }

            // 真实接口失败时返回空数据，不使用测试数据
            return {
              'success': false,
              'message': data['message'] ?? LocalizationService.t('fetch_failed'),
              'data': _getEmptyMessagesData(),
              'error': data['message'] ?? LocalizationService.t('fetch_failed'),
            };
          }
        } else {
          // HTTP错误时返回空数据，不使用测试数据
          return {
            'success': false,
            'message': 'HTTP ${response.statusCode}',
            'data': _getEmptyMessagesData(),
            'error': 'HTTP ${response.statusCode}',
          };
        }
      }
    } catch (e) {
      // 处理API错误，特别是token过期错误
      final errorResult = await _handleApiError(e, context: context);
      if (errorResult['isTokenExpired'] == true) {
        return errorResult;
      }

      // 网络请求失败，尝试从缓存获取
      final cachedData = await CacheService.getMessagesData();
      if (cachedData != null) {
        return {
          'success': true,
          'message': LocalizationService.t('fetch_success_offline_cache'),
          'data': cachedData,
          'fromCache': true,
        };
      }

      // 没有缓存数据，返回错误
      return {
        'success': false,
        'message': LocalizationService.t('network_connection_failed'),
        'data': null,
        'error': e.toString(),
      };
    }
  }

  // 获取工作台常用应用
  static Future<Map<String, dynamic>> getWorkspaceApps({
    bool forceRefresh = false,
    bool? useMockData,
    BuildContext? context,
  }) async {
    await initialize();

    // 如果没有指定useMockData，则使用全局设置
    final shouldUseMockData = useMockData ?? await getUseMockData();

    try {
      // 检查网络状态
      final networkStatus = NetworkStatusService();
      await networkStatus.initialize(); // 确保网络状态服务已初始化
      final isNetworkAvailable = networkStatus.isConnected;

      // 如果不是强制刷新，先尝试从缓存获取
      if (!forceRefresh) {
        _logger.info(LocalizationService.t('debug_check_workspace_cache'), tag: 'WORKSPACE');
        final cachedData = await CacheService.getWorkspaceData();
        if (cachedData != null) {
          _logger.info(LocalizationService.t('debug_found_workspace_cache'), tag: 'WORKSPACE');
          // 如果有缓存且网络可用，提示使用缓存
          // 如果网络不可用，提示离线缓存
          final message = isNetworkAvailable
              ? LocalizationService.t('fetch_success_cache')
              : LocalizationService.t('fetch_success_offline_cache');
          return {
            'success': true,
            'message': message,
            'data': cachedData,
            'fromCache': true,
            'isOffline': !isNetworkAvailable,
          };
        } else {
          _logger.info(LocalizationService.t('debug_not_found_workspace_cache'), tag: 'WORKSPACE');
        }
      } else {
        _logger.info(LocalizationService.t('debug_force_refresh_skip_cache'), tag: 'WORKSPACE');
      }

      // 如果网络不可用且没有缓存，直接返回网络错误
      if (!isNetworkAvailable) {
        return {
          'success': false,
          'message': LocalizationService.t('network_connection_failed'),
          'data': null,
          'error': 'Network not available',
          'isOffline': true,
        };
      }

      if (shouldUseMockData) {
        await ApiDelayManager.withMinDelay(
          Future.value(null),
          type: 'data',
        );

        final result = {
          'success': true,
          'message': LocalizationService.t('fetch_success'),
          'data': {
            'favoriteApps': [
              {
                'id': 1,
                'name': '日程管理',
                'icon': 'calendar_today',
                'color': '0xFF3366CC',
                'url': '', // 改为本地详情页
                'sort': 1,
                'description': '管理日程安排',
              },
              {
                'id': 2,
                'name': '任务协作',
                'icon': 'assignment',
                'color': '0xFFFF9900',
                'url': '', // 改为本地详情页
                'sort': 2,
                'description': '团队任务协作',
              },
              {
                'id': 3,
                'name': '文档中心',
                'icon': 'description',
                'color': '0xFF52C41A',
                'url': '', // 改为本地详情页
                'sort': 3,
                'description': '文档管理中心',
              },
              {
                'id': 4,
                'name': '客户管理',
                'icon': 'people',
                'color': '0xFF9C27B0',
                'url': '', // 本地详情页
                'sort': 4,
                'description': '客户关系管理系统',
              },
              {
                'id': 5,
                'name': '销售统计',
                'icon': 'bar_chart',
                'color': '0xFF00BCD4',
                'url': '', // 本地详情页
                'sort': 5,
                'description': '销售数据统计分析',
              },
              {
                'id': 6,
                'name': '考勤打卡',
                'icon': 'access_time',
                'color': '0xFFFF5722',
                'url': '', // 本地详情页
                'sort': 6,
                'description': '员工考勤管理',
              },
            ],
          },
        };

        // 保存到缓存
        final responseData = result['data'] as Map<String, dynamic>;
        await CacheService.saveWorkspaceData(responseData);

        _logger.info('工作台应用模拟数据返回: ${result.keys}', tag: 'WORKSPACE');
        _logger.info('常用应用数量: ${(responseData['favoriteApps'] as List).length}', tag: 'WORKSPACE');
        return result;
      } else {
        // 真实接口请求
        final baseUrl = await getBaseUrl();
        final headers = await _getAuthHeaders();
        final url = '$baseUrl/workspace/apps';

        _logRequest('GET', url, headers, null);

        // 确保最小延迟，让用户看到加载状态
        final requestFuture = _httpClient.get(
          url,
          headers: headers,
        );

        final response = await ApiDelayManager.withMinDelay(
          requestFuture,
          type: 'data',
        );

        _logResponse(url, response.statusCode, response.json);

        if (response.statusCode == 200) {
          final data = response.json;
          _logger.info('工作台应用接口响应数据结构: ${data.keys}', tag: 'WORKSPACE');
          _logger.info('工作台应用接口完整响应数据: $data', tag: 'WORKSPACE');

          if (data['success'] == true) {
            // 处理API返回的数据，确保即使数据为空也能正常处理
            final responseData = data['data'] ?? {};
            final processedData = _processWorkspaceData(responseData);

            // 保存到缓存
            await CacheService.saveWorkspaceData(processedData);

            final result = {
              'success': true,
              'message': data['message'] ?? LocalizationService.t('fetch_success'),
              'data': processedData,
            };
            _logger.info('工作台应用接口成功: ${result.keys}', tag: 'WORKSPACE');
            _logger.info('工作台应用数据内容: ${result['data']}', tag: 'WORKSPACE');
            return result;
          } else {
            _logger.warning('工作台应用接口业务失败: ${data['message']}', tag: 'WORKSPACE');

            // 检查是否为token相关错误（后端可能返回200状态码但业务失败）
            final errorMessage = data['message'] ?? '';
            final tokenHandler = TokenExpiryHandler();
            if (tokenHandler.isTokenExpiredError(errorMessage)) {
              _logger.warning('检测到token过期错误（业务层）: $errorMessage', tag: 'WORKSPACE');

              // 手动触发token过期处理
              if (context != null && context.mounted) {
                await tokenHandler.handleTokenExpiry(context);
              }

              return {
                'success': false,
                'message': LocalizationService.t('token_expired'),
                'isTokenExpired': true,
              };
            }

            // 真实接口失败时返回空数据，不使用测试数据
            return {
              'success': false,
              'message': data['message'] ?? LocalizationService.t('fetch_failed'),
              'data': _getEmptyWorkspaceData(),
              'error': data['error'],
            };
          }
        } else {
          _logger.error('工作台应用接口HTTP错误: ${response.statusCode}', tag: 'WORKSPACE');
          // HTTP错误时返回空数据，不使用测试数据
          return {
            'success': false,
            'message': 'HTTP ${response.statusCode}',
            'data': _getEmptyWorkspaceData(),
            'error': 'HTTP ${response.statusCode}',
          };
        }
      }
    } catch (e) {
      // 处理API错误，特别是token过期错误
      final errorResult = await _handleApiError(e, context: context);
      if (errorResult['isTokenExpired'] == true) {
        return errorResult;
      }

      // 网络请求失败，尝试从缓存获取
      final cachedData = await CacheService.getWorkspaceData();
      if (cachedData != null) {
        return {
          'success': true,
          'message': LocalizationService.t('fetch_success_offline_cache'),
          'data': cachedData,
          'fromCache': true,
        };
      }

      // 没有缓存数据，返回错误
      return {
        'success': false,
        'message': '${LocalizationService.t('network_connection_failed')}：${e.toString()}',
        'data': null,
        'error': e.toString(),
      };
    }
  }

  // 保存常用应用设置
  static Future<Map<String, dynamic>> saveFavoriteApps(
    List<String> appIds, {
    bool? useMockData,
  }) async {
    await initialize();

    // 如果没有指定useMockData，则使用全局设置
    final shouldUseMockData = useMockData ?? await getUseMockData();

    try {
      if (shouldUseMockData) {
        await ApiDelayManager.withMinDelay(
          Future.value(null),
          type: 'quick',
        );

        return {
          'success': true,
          'message': LocalizationService.t('favorite_apps_saved'),
        };
      } else {
        // 真实接口请求
        final baseUrl = await getBaseUrl();
        final response = await _httpClient.post(
          '$baseUrl/apps/favorites',
          headers: await _getAuthHeaders(),
          body: {'appIds': appIds},
        );

        if (response.statusCode == 200) {
          final data = response.json;
          if (data['success'] == true) {
            return {
              'success': true,
              'message': data['message'] ?? LocalizationService.t('favorite_apps_saved'),
            };
          } else {
            throw Exception(data['message'] ?? LocalizationService.t('save_failed'));
          }
        } else {
          throw Exception('HTTP ${response.statusCode}');
        }
      }
    } catch (e) {
      return {
        'success': false,
        'message': '${LocalizationService.t('save_failed')}：${e.toString()}',
      };
    }
  }

  // 保存应用排序
  static Future<Map<String, dynamic>> saveAppSort(
    List<Map<String, dynamic>> apps, {
    bool? useMockData,
  }) async {
    await initialize();

    // 如果没有指定useMockData，则使用全局设置
    final shouldUseMockData = useMockData ?? await getUseMockData();

    try {
      if (shouldUseMockData) {
        await ApiDelayManager.withMinDelay(
          Future.value(null),
          type: 'quick',
        );

        return {
          'success': true,
          'message': LocalizationService.t('sort_save_success'),
        };
      } else {
        // 真实接口请求
        final baseUrl = await getBaseUrl();
        final response = await _httpClient.post(
          '$baseUrl/apps/sort',
          headers: await _getAuthHeaders(),
          body: {'apps': apps},
        );

        if (response.statusCode == 200) {
          final data = response.json;
          if (data['success'] == true) {
            return {
              'success': true,
              'message': data['message'] ?? LocalizationService.t('sort_save_success'),
            };
          } else {
            throw Exception(data['message'] ?? LocalizationService.t('save_failed'));
          }
        } else {
          throw Exception('HTTP ${response.statusCode}');
        }
      }
    } catch (e) {
      return {
        'success': false,
        'message': '${LocalizationService.t('save_failed')}：${e.toString()}',
      };
    }
  }

  // 测试服务器连接
  static Future<Map<String, dynamic>> testConnection(
    String serverUrl, {
    bool? useMockData,
  }) async {
    // 如果没有指定useMockData，则使用全局设置
    final shouldUseMockData = useMockData ?? await getUseMockData();

    try {
      if (shouldUseMockData) {
        await ApiDelayManager.withMinDelay(
          Future.value(null),
          customDelay: 2000, // 连接测试需要更长延迟
        );

        return {
          'success': true,
          'message': LocalizationService.t('connection_success'),
          'data': {
            'serverInfo': {
              'name': LocalizationService.t('enterprise_office_system'),
              'version': '2.1.0',
              'logo': 'https://picsum.photos/100/100?random=logo',
              'description': LocalizationService.t('efficient_collaboration'),
              'company': LocalizationService.t('tech_company'),
              'copyright': LocalizationService.t('copyright_text'),
            },
          },
        };
      } else {
        // 真实接口请求
        final response = await _httpClient.get('$serverUrl/api/system/ping');

        if (response.statusCode == 200) {
          final data = response.json;
          if (data['success'] == true) {
            return {
              'success': true,
              'message': data['message'] ?? LocalizationService.t('connection_success'),
              'data': data['data'],
            };
          } else {
            throw Exception(data['message'] ?? LocalizationService.t('connection_failed'));
          }
        } else {
          throw Exception('HTTP ${response.statusCode}');
        }
      }
    } catch (e) {
      return {
        'success': false,
        'message': '${LocalizationService.t('connection_failed')}：${e.toString()}',
      };
    }
  }

  // ========== 数据处理和空状态辅助方法 ==========

  /// 处理首页数据，确保字段完整性
  static Map<String, dynamic> _processHomeData(Map<String, dynamic> data) {
    final banners = data['banners'] as List<dynamic>? ?? [];
    final news = data['news'] as List<dynamic>? ?? [];

    return {
      'banners': banners.map((banner) => _processBannerData(banner)).toList(),
      'news': news.map((newsItem) => _processNewsData(newsItem)).toList(),
    };
  }

  /// 处理轮播图数据
  static Map<String, dynamic> _processBannerData(dynamic banner) {
    final bannerData = banner as Map<String, dynamic>? ?? {};
    return {
      'id': bannerData['id'] ?? 0,
      'title': bannerData['title'] ?? '无标题',
      'image': bannerData['image'] ?? '',
      'url': bannerData['url'] ?? '', // 如果URL为空，点击时不跳转
      'description': bannerData['description'] ?? '',
    };
  }

  /// 处理新闻数据
  static Map<String, dynamic> _processNewsData(dynamic news) {
    final newsData = news as Map<String, dynamic>? ?? {};
    return {
      'id': newsData['id'] ?? 0,
      'title': newsData['title'] ?? '无标题',
      'summary': newsData['summary'] ?? '',
      'publishTime': newsData['publishTime'] ?? '',
      'category': newsData['category'] ?? '未分类',
      'url': newsData['url'] ?? '', // 如果URL为空，点击时不跳转
    };
  }

  /// 处理应用列表数据
  static Map<String, dynamic> _processAppsData(Map<String, dynamic> data) {
    final categories = data['categories'] as List<dynamic>? ?? [];

    return {
      'categories': categories.map((category) => _processCategoryData(category)).toList(),
    };
  }

  /// 处理应用分类数据
  static Map<String, dynamic> _processCategoryData(dynamic category) {
    final categoryData = category as Map<String, dynamic>? ?? {};
    final apps = categoryData['apps'] as List<dynamic>? ?? [];

    return {
      'title': categoryData['title'] ?? '未分类',
      'icon': categoryData['icon'] ?? 'apps',
      'apps': apps.map((app) => _processAppData(app)).toList(),
    };
  }

  /// 处理应用数据
  static Map<String, dynamic> _processAppData(dynamic app) {
    final appData = app as Map<String, dynamic>? ?? {};
    return {
      'name': appData['name'] ?? '未知应用',
      'icon': appData['icon'] ?? 'apps',
      'color': appData['color'] ?? '#2196F3',
      'description': appData['description'] ?? '',
      'url': appData['url'] ?? '', // 如果URL为空，点击时不跳转
    };
  }

  /// 处理消息数据
  static Map<String, dynamic> _processMessagesData(Map<String, dynamic> data) {
    final messages = data['messages'] as List<dynamic>? ?? [];

    return {
      'messages': messages.map((message) => _processMessageData(message)).toList(),
    };
  }

  /// 处理单条消息数据
  static Map<String, dynamic> _processMessageData(dynamic message) {
    final messageData = message as Map<String, dynamic>? ?? {};
    return {
      'id': messageData['id'] ?? 0,
      'title': messageData['title'] ?? '无标题',
      'content': messageData['content'] ?? '',
      'type': messageData['type'] ?? 'system',
      'publishTime': messageData['publishTime'] ?? '',
      'unreadCount': messageData['unreadCount'] ?? 0,
      'url': messageData['url'] ?? '', // 如果URL为空，点击时不跳转
    };
  }

  /// 处理工作台数据
  static Map<String, dynamic> _processWorkspaceData(Map<String, dynamic> data) {
    final favoriteApps = data['favoriteApps'] as List<dynamic>? ?? [];

    return {
      'favoriteApps': favoriteApps.map((app) => _processWorkspaceAppData(app)).toList(),
    };
  }

  /// 处理工作台应用数据
  static Map<String, dynamic> _processWorkspaceAppData(dynamic app) {
    final appData = app as Map<String, dynamic>? ?? {};
    return {
      'name': appData['name'] ?? '未知应用',
      'icon': appData['icon'] ?? 'apps',
      'color': appData['color'] ?? '#2196F3',
      'description': appData['description'] ?? '',
      'url': appData['url'] ?? '', // 如果URL为空，点击时不跳转
      'sort': appData['sort'] ?? 0,
    };
  }

  // ========== 空数据返回方法 ==========

  /// 返回空的首页数据
  static Map<String, dynamic> _getEmptyHomeData() {
    return {
      'banners': <Map<String, dynamic>>[],
      'news': <Map<String, dynamic>>[],
    };
  }

  /// 返回空的应用列表数据
  static Map<String, dynamic> _getEmptyAppsData() {
    return {
      'categories': <Map<String, dynamic>>[],
    };
  }

  /// 返回空的消息数据
  static Map<String, dynamic> _getEmptyMessagesData() {
    return {
      'messages': <Map<String, dynamic>>[],
    };
  }

  /// 返回空的工作台数据
  static Map<String, dynamic> _getEmptyWorkspaceData() {
    return {
      'favoriteApps': <Map<String, dynamic>>[],
    };
  }
}
