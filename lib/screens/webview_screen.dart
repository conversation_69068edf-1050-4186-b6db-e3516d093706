import 'dart:async';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../constants/app_theme.dart';
import '../services/localization_service.dart';
import '../services/api_service.dart';
import '../services/error_log_service.dart';
import 'page_not_found_screen.dart';

class WebViewScreen extends StatefulWidget {
  final String title;
  final String url;

  const WebViewScreen({super.key, required this.title, required this.url});

  @override
  State<WebViewScreen> createState() => _WebViewScreenState();
}

class _WebViewScreenState extends State<WebViewScreen> {
  WebViewController? _controller;
  bool _isLoading = true;
  bool _isLocalFile = false;
  bool _isRefreshing = false;
  bool _hasError = false;
  String? _errorMessage;
  DateTime? _lastRefreshTime;
  Timer? _loadingTimer;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }

  Future<void> _initializeWebView() async {
    // 取消之前的超时定时器
    _loadingTimer?.cancel();

    // 重置错误状态
    if (mounted) {
      setState(() {
        _hasError = false;
        _errorMessage = null;
        _isLoading = true;
      });
    }

    // 设置加载超时定时器（30秒）
    _loadingTimer = Timer(const Duration(seconds: 30), () {
      if (mounted && _isLoading && !_hasError) {
        debugPrint('WebViewScreen: 加载超时，显示404页面');
        setState(() {
          _hasError = true;
          _errorMessage = 'Loading timeout';
          _isLoading = false;
        });
      }
    });

    _controller =
        WebViewController()
          ..setJavaScriptMode(JavaScriptMode.unrestricted)
          ..setUserAgent('Mozilla/5.0 (Linux; Android 10; Mobile) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36')
          ..setNavigationDelegate(
            NavigationDelegate(
              onProgress: (int progress) {
                // Update loading progress
              },
              onPageStarted: (String url) {
                if (mounted) {
                  setState(() {
                    _isLoading = true;
                    _hasError = false;
                  });
                }
              },
              onPageFinished: (String url) async {
                // 取消超时定时器
                _loadingTimer?.cancel();

                // 检查页面内容是否为JSON错误响应
                await _checkForJsonError();

                if (mounted) {
                  setState(() {
                    _isLoading = false;
                  });
                }
              },
              onWebResourceError: (WebResourceError error) {
                debugPrint('WebViewScreen: WebResource错误 - ${error.description}, 错误类型: ${error.errorType}, 是否本地文件: $_isLocalFile');

                // 检查是否是HTTP明文不允许的错误
                if (error.description.contains('ERR_CLEARTEXT_NOT_PERMITTED')) {
                  debugPrint('WebViewScreen: 检测到HTTP明文不允许错误，尝试HTTPS');
                  _tryHttpsAlternative();
                  return;
                }

                // 忽略常见的资源加载错误，这些不影响主页面显示
                if (error.description.contains('ERR_BLOCKED_BY_ORB') ||
                    error.description.contains('ERR_UNKNOWN_URL_SCHEME') ||
                    error.description.contains('ERR_ABORTED')) {
                  debugPrint('WebViewScreen: 忽略资源错误 - ${error.description}');
                  return;
                }

                // 只有在加载主页面失败时才显示404页面
                // 对于本地文件，不应该因为内部资源失败而显示404
                if (mounted && !_isLocalFile) {
                  // 只处理主要的页面加载错误，忽略资源加载错误
                  if (error.errorType == WebResourceErrorType.hostLookup ||
                      error.errorType == WebResourceErrorType.timeout ||
                      error.errorType == WebResourceErrorType.fileNotFound ||
                      error.errorType == WebResourceErrorType.badUrl) {
                    debugPrint('WebViewScreen: 显示404页面 - ${error.description}');
                    // 取消超时定时器
                    _loadingTimer?.cancel();
                    setState(() {
                      _hasError = true;
                      _errorMessage = error.description;
                      _isLoading = false;
                    });
                  } else {
                    debugPrint('WebViewScreen: 忽略资源错误 - ${error.description}');
                  }
                } else if (_isLocalFile) {
                  debugPrint('WebViewScreen: 本地文件资源错误，忽略 - ${error.description}');
                }
              },
            ),
          );

    // Check if it is a local HTML file
    _isLocalFile = widget.url.startsWith('assets/');
    if (_isLocalFile) {
      await _loadLocalHtml();
    } else {
      await _loadRemoteUrl();
    }
  }

  Future<void> _loadRemoteUrl() async {
    try {
      final processedUrl = await _processUrl(widget.url);

      // 验证URL格式
      final uri = Uri.tryParse(processedUrl);
      if (uri == null || !uri.hasScheme || !uri.hasAuthority) {
        throw FormatException('无效的URL格式: $processedUrl');
      }

      _controller?.loadRequest(uri);
      debugPrint('WebViewScreen: 开始加载URL - $processedUrl');

    } catch (e) {
      debugPrint('WebViewScreen: URL处理失败 - ${widget.url}, 错误: $e');
      if (mounted) {
        setState(() {
          _hasError = true;
          _errorMessage = 'URL格式错误: ${widget.url}\n错误详情: $e';
          _isLoading = false;
        });
      }
    }
  }

  /// 检查页面内容是否为JSON错误响应
  Future<void> _checkForJsonError() async {
    try {
      if (_controller == null || _isLocalFile) return;

      // 执行JavaScript获取页面内容
      final pageContent = await _controller!.runJavaScriptReturningResult(
        'document.body.innerText || document.body.textContent || ""'
      );

      if (pageContent is String && pageContent.isNotEmpty) {
        final content = pageContent.trim();

        debugPrint('WebViewScreen: 页面内容检查 - $content');

        // 移除可能的引号包装并处理转义字符
        String cleanContent = content;
        if (content.startsWith('"') && content.endsWith('"')) {
          cleanContent = content.substring(1, content.length - 1);
          // 处理转义字符
          cleanContent = cleanContent.replaceAll('\\"', '"').replaceAll('\\n', '\n');
        }

        // 检查是否是JSON格式的错误响应
        if (cleanContent.startsWith('{') && cleanContent.endsWith('}')) {
          // 使用简单的字符串匹配检查常见的错误模式
          if (cleanContent.contains('"success": false') ||
              cleanContent.contains('"success":false') ||
              cleanContent.contains('接口不存在') ||
              cleanContent.contains('Not Found') ||
              cleanContent.contains('页面不存在') ||
              cleanContent.contains('404') ||
              cleanContent.contains('找不到') ||
              cleanContent.contains('does not exist')) {

            debugPrint('WebViewScreen: 检测到JSON错误响应，显示404页面');

            // 尝试提取错误消息
            String errorMessage = '页面不存在或接口错误';

            // 简单提取message字段
            final messageMatch = RegExp(r'"message"\s*:\s*"([^"]*)"').firstMatch(cleanContent);
            if (messageMatch != null) {
              errorMessage = messageMatch.group(1) ?? errorMessage;
            } else {
              // 尝试提取error字段
              final errorMatch = RegExp(r'"error"\s*:\s*"([^"]*)"').firstMatch(cleanContent);
              if (errorMatch != null) {
                errorMessage = errorMatch.group(1) ?? errorMessage;
              }
            }

            // 记录WebView错误到错误日志
            await ErrorLogService.logAppError(
              message: 'WebView页面加载错误: $errorMessage',
              additionalInfo: {
                'url': widget.url,
                'title': widget.title,
                'errorType': 'webview_json_error',
                'responseContent': cleanContent,
                'timestamp': DateTime.now().toIso8601String(),
              },
            );

            if (mounted) {
              debugPrint('WebViewScreen: 设置错误状态，将显示404页面');
              setState(() {
                _hasError = true;
                _errorMessage = errorMessage;
                _isLoading = false;
              });
            }
            return;
          }
        }

        // 检查页面是否只包含很少的内容（可能是错误页面）
        if (content.length < 100 &&
            (content.contains('error') ||
             content.contains('not found') ||
             content.contains('404') ||
             content.contains('错误'))) {

          debugPrint('WebViewScreen: 检测到简短错误内容，显示404页面 - $content');

          // 记录WebView错误到错误日志
          await ErrorLogService.logAppError(
            message: 'WebView页面加载错误: ${content.isNotEmpty ? content : '页面加载失败'}',
            additionalInfo: {
              'url': widget.url,
              'title': widget.title,
              'errorType': 'webview_short_error',
              'responseContent': content,
              'timestamp': DateTime.now().toIso8601String(),
            },
          );

          if (mounted) {
            setState(() {
              _hasError = true;
              _errorMessage = content.isNotEmpty ? content : '页面加载失败';
              _isLoading = false;
            });
          }
        }
      }
    } catch (e) {
      debugPrint('WebViewScreen: 检查JSON错误时出现异常 - $e');
      // 如果检查过程中出现错误，不影响正常流程
    }
  }

  /// 尝试HTTPS备用方案
  Future<void> _tryHttpsAlternative() async {
    try {
      String originalUrl = widget.url;
      String httpsUrl;

      // 如果原始URL是相对路径，先转换为完整URL
      if (originalUrl.startsWith('/')) {
        final baseUrl = await ApiService.getBaseUrl();
        final serverUrl = baseUrl.replaceAll('/api', '');
        httpsUrl = serverUrl.replaceFirst('http://', 'https://') + originalUrl;
      } else if (originalUrl.startsWith('http://')) {
        // 直接将HTTP替换为HTTPS
        httpsUrl = originalUrl.replaceFirst('http://', 'https://');
      } else {
        // 其他情况，添加HTTPS协议
        httpsUrl = 'https://$originalUrl';
      }

      debugPrint('WebViewScreen: 尝试HTTPS备用方案 - 原始: $originalUrl, HTTPS: $httpsUrl');

      final uri = Uri.tryParse(httpsUrl);
      if (uri != null && uri.hasScheme && uri.hasAuthority) {
        _controller?.loadRequest(uri);
      } else {
        throw FormatException('HTTPS URL格式无效: $httpsUrl');
      }

    } catch (e) {
      debugPrint('WebViewScreen: HTTPS备用方案失败 - $e');
      if (mounted) {
        setState(() {
          _hasError = true;
          _errorMessage = 'HTTP和HTTPS都无法访问: ${widget.url}';
          _isLoading = false;
        });
      }
    }
  }

  /// 处理各种URL格式，返回完整的URL
  Future<String> _processUrl(String originalUrl) async {
    String url = originalUrl.trim();

    // 1. 如果已经是完整的URL（包含协议），直接使用
    if (url.startsWith('http://') || url.startsWith('https://')) {
      debugPrint('WebViewScreen: 完整URL直接使用 - $url');
      return url;
    }

    // 2. 如果是相对路径（以/开头），使用服务器配置地址
    if (url.startsWith('/')) {
      final baseUrl = await ApiService.getBaseUrl();
      // 移除API路径，只保留服务器地址
      final serverUrl = baseUrl.replaceAll('/api', '');
      url = '$serverUrl$url';
      debugPrint('WebViewScreen: 相对路径转换 - 原始: $originalUrl, 转换后: $url');
      return url;
    }

    // 3. 如果包含域名特征（包含点号），添加https协议
    if (url.contains('.')) {
      // 检查是否是IP地址格式
      final ipRegex = RegExp(r'^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}');
      if (ipRegex.hasMatch(url)) {
        // IP地址，使用http协议（通常内网IP不支持https）
        url = 'http://$url';
        debugPrint('WebViewScreen: IP地址添加http协议 - 原始: $originalUrl, 转换后: $url');
      } else {
        // 域名，使用https协议
        url = 'https://$url';
        debugPrint('WebViewScreen: 域名添加https协议 - 原始: $originalUrl, 转换后: $url');
      }
      return url;
    }

    // 4. 如果包含端口号（格式如：localhost:8080）
    if (url.contains(':') && !url.contains('://')) {
      url = 'http://$url';
      debugPrint('WebViewScreen: 地址:端口格式添加http协议 - 原始: $originalUrl, 转换后: $url');
      return url;
    }

    // 5. 其他情况，可能是不完整的域名，尝试添加https
    url = 'https://$url';
    debugPrint('WebViewScreen: 默认添加https协议 - 原始: $originalUrl, 转换后: $url');
    return url;
  }

  Future<void> _loadLocalHtml() async {
    try {
      // Extract the base file path without query parameters
      final uri = Uri.parse(widget.url);
      final basePath = uri.path;

      debugPrint('WebViewScreen: 尝试加载本地文件 - $basePath');

      final String htmlContent = await DefaultAssetBundle.of(
        context,
      ).loadString(basePath);

      debugPrint('WebViewScreen: 本地文件加载成功，长度: ${htmlContent.length}');

      // Inject query parameters into the HTML if needed
      String modifiedHtml = htmlContent;
      if (uri.hasQuery) {
        final queryParams = uri.queryParameters;
        debugPrint('WebViewScreen: 注入查询参数 - $queryParams');

        // Add JavaScript to handle query parameters
        final scriptTag = '''
<script>
  window.urlParams = ${_mapToJavaScript(queryParams)};
  // Trigger page initialization with parameters
  document.addEventListener('DOMContentLoaded', function() {
    if (typeof initializeWithParams === 'function') {
      initializeWithParams(window.urlParams);
    }
  });
</script>
''';
        // Insert the script before the closing head tag
        modifiedHtml = htmlContent.replaceFirst('</head>', '$scriptTag</head>');
      }

      await _controller?.loadHtmlString(modifiedHtml);
      debugPrint('WebViewScreen: HTML字符串加载完成');

    } catch (e) {
      debugPrint('WebViewScreen: 加载本地文件失败 - $e');

      if (mounted) {
        setState(() {
          _hasError = true;
          _errorMessage = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  String _mapToJavaScript(Map<String, String> map) {
    final entries = map.entries
        .map((e) => '"${e.key}": "${e.value}"')
        .join(', ');
    return '{$entries}';
  }

  void _refreshWebView() {
    // 检查是否在刷新中或距离上次刷新不足3秒
    final now = DateTime.now();
    if (_isRefreshing ||
        (_lastRefreshTime != null &&
            now.difference(_lastRefreshTime!).inSeconds < 3)) {
      return;
    }

    setState(() {
      _isRefreshing = true;
      _isLoading = true;
    });

    _lastRefreshTime = now;

    if (_isLocalFile) {
      // For local files, reload HTML content
      _loadLocalHtml();
    } else {
      // For network URLs, use standard reload method
      _controller?.reload();
    }

    // Show refresh hint
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(LocalizationService.t('refreshing_page')),
        duration: const Duration(seconds: 1),
        backgroundColor: AppTheme.primaryColor,
      ),
    );

    // 3秒后重新启用刷新按钮
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _isRefreshing = false;
        });
      }
    });
  }

  @override
  void dispose() {
    _loadingTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 如果有错误，显示404页面
    if (_hasError) {
      return PageNotFoundScreen(
        title: widget.title,
        description: LocalizationService.t('webview_load_failed'),
        errorMessage: _errorMessage,
        onRetry: () {
          _initializeWebView();
        },
        onGoBack: () => Navigator.of(context).pop(),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.arrow_back),
        ),
        actions: [
          IconButton(
            onPressed: _isRefreshing ? null : _refreshWebView,
            icon: _isRefreshing
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
                    ),
                  )
                : const Icon(Icons.refresh),
            style: IconButton.styleFrom(
              disabledForegroundColor: Theme.of(context).brightness == Brightness.dark
                  ? AppTheme.darkTextSecondary
                  : AppTheme.textSecondary,
            ),
          ),
        ],
      ),
      body: Stack(
          children: [
            if (_controller != null) WebViewWidget(controller: _controller!),
            if (_isLoading)
              Container(
                color: AppTheme.backgroundColor,
                child: const Center(
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(
                      AppTheme.primaryColor,
                    ),
                  ),
                ),
              ),
          ],
        ),
    );
  }
}
