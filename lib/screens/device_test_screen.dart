import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../services/webview_bridge_service.dart';
import '../plugins/real_plugin_manager.dart';

/// 设备功能测试页面
/// 用于测试WebView调用设备功能
class DeviceTestScreen extends StatefulWidget {
  final String? initialFunction; // 初始要测试的功能
  
  const DeviceTestScreen({
    Key? key,
    this.initialFunction,
  }) : super(key: key);

  @override
  State<DeviceTestScreen> createState() => _DeviceTestScreenState();
}

class _DeviceTestScreenState extends State<DeviceTestScreen> {
  late WebViewController _controller;
  bool _isLoading = true;
  String _currentTitle = '设备功能测试';
  String? _rightButtonText;
  VoidCallback? _rightButtonAction;

  @override
  void initState() {
    super.initState();
    _setupTitleBarCallbacks();
    _initializeWebView();
  }

  void _setupTitleBarCallbacks() {
    // 设置标题栏控制回调
    RealPluginManager.setTitleBarCallbacks(
      onTitleChange: (String title) {
        setState(() {
          _currentTitle = title;
        });
      },
      onRightButtonChange: (String? text, VoidCallback? action) {
        setState(() {
          _rightButtonText = text;
          _rightButtonAction = action;
        });
      },
    );
  }

  void _initializeWebView() {
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            setState(() {
              _isLoading = true;
            });
          },
          onPageFinished: (String url) {
            setState(() {
              _isLoading = false;
            });

            // 注入原生API
            WebViewBridgeService.injectNativeAPI();

            // 如果指定了初始功能，滚动到对应位置
            if (widget.initialFunction != null) {
              _scrollToFunction(widget.initialFunction!);
            }
          },
          onWebResourceError: (WebResourceError error) {
            debugPrint('WebView error: ${error.description}');
          },
        ),
      )
      ..setBackgroundColor(const Color(0x00000000));

    // 注册JavaScript桥接
    WebViewBridgeService.registerBridge(_controller);
    
    // 加载测试页面
    _loadTestPage();
  }

  void _loadTestPage() {
    // WebView不能直接加载带锚点的Asset文件，所以先加载基础文件
    _controller.loadFlutterAsset('assets/html/webview_api_test.html');
  }

  void _scrollToFunction(String functionName) {
    // 延迟执行，确保页面已加载完成
    Future.delayed(const Duration(milliseconds: 1000), () {
      _controller.runJavaScript('''
        if (typeof scrollToSection === 'function') {
          scrollToSection('$functionName');
        }
      ''');
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_currentTitle),
        backgroundColor: const Color(0xFF667eea),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          // 动态右侧按钮
          if (_rightButtonText != null)
            TextButton(
              onPressed: _rightButtonAction,
              child: Text(
                _rightButtonText!,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              _controller.reload();
            },
            tooltip: '刷新页面',
          ),
          PopupMenuButton<String>(
            onSelected: (String function) {
              _scrollToFunction(function);
            },
            itemBuilder: (BuildContext context) => [
              const PopupMenuItem(
                value: 'camera',
                child: Row(
                  children: [
                    Icon(Icons.camera_alt, size: 20),
                    SizedBox(width: 8),
                    Text('相机功能'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'qrcode',
                child: Row(
                  children: [
                    Icon(Icons.qr_code_scanner, size: 20),
                    SizedBox(width: 8),
                    Text('二维码'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'bluetooth',
                child: Row(
                  children: [
                    Icon(Icons.bluetooth, size: 20),
                    SizedBox(width: 8),
                    Text('蓝牙打印'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'nfc',
                child: Row(
                  children: [
                    Icon(Icons.nfc, size: 20),
                    SizedBox(width: 8),
                    Text('NFC功能'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'location',
                child: Row(
                  children: [
                    Icon(Icons.location_on, size: 20),
                    SizedBox(width: 8),
                    Text('定位服务'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'device',
                child: Row(
                  children: [
                    Icon(Icons.phone_android, size: 20),
                    SizedBox(width: 8),
                    Text('设备信息'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'system',
                child: Row(
                  children: [
                    Icon(Icons.settings, size: 20),
                    SizedBox(width: 8),
                    Text('系统功能'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'titlebar',
                child: Row(
                  children: [
                    Icon(Icons.title, size: 20),
                    SizedBox(width: 8),
                    Text('标题栏控制'),
                  ],
                ),
              ),
            ],
            tooltip: '快速导航',
          ),
        ],
      ),
      body: Stack(
        children: [
          WebViewWidget(controller: _controller),
          if (_isLoading)
            Container(
              color: Colors.white,
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF667eea)),
                    ),
                    SizedBox(height: 16),
                    Text(
                      '正在加载设备功能测试页面...',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showFunctionMenu();
        },
        backgroundColor: const Color(0xFF667eea),
        child: const Icon(Icons.menu, color: Colors.white),
        tooltip: '功能菜单',
      ),
    );
  }

  void _showFunctionMenu() {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '设备功能测试',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  _buildFunctionChip('相机功能', Icons.camera_alt, 'camera'),
                  _buildFunctionChip('二维码', Icons.qr_code_scanner, 'qrcode'),
                  _buildFunctionChip('蓝牙打印', Icons.bluetooth, 'bluetooth'),
                  _buildFunctionChip('NFC功能', Icons.nfc, 'nfc'),
                  _buildFunctionChip('定位服务', Icons.location_on, 'location'),
                  _buildFunctionChip('设备信息', Icons.phone_android, 'device'),
                  _buildFunctionChip('系统功能', Icons.settings, 'system'),
                  _buildFunctionChip('标题栏控制', Icons.title, 'titlebar'),
                ],
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    _controller.runJavaScript('window.scrollTo(0, 0);');
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF667eea),
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('返回顶部'),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFunctionChip(String label, IconData icon, String function) {
    return ActionChip(
      avatar: Icon(icon, size: 18),
      label: Text(label),
      onPressed: () {
        Navigator.pop(context);
        _scrollToFunction(function);
      },
      backgroundColor: const Color(0xFFE3F2FD),
      labelStyle: const TextStyle(color: Color(0xFF1976D2)),
    );
  }
}

/// 设备功能测试入口工具类
class DeviceTestUtils {
  /// 从应用页面打开设备功能测试
  static void openDeviceTest(BuildContext context, {String? function}) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => DeviceTestScreen(initialFunction: function),
      ),
    );
  }

  /// 获取功能描述
  static String getFunctionDescription(String function) {
    switch (function) {
      case 'camera':
        return '测试拍照、录像、相册选择等相机功能';
      case 'qrcode':
        return '测试二维码扫描、生成、批量处理功能';
      case 'bluetooth':
        return '测试蓝牙设备连接和打印功能';
      case 'nfc':
        return '测试NFC标签读写和数据交换功能';
      case 'location':
        return '测试位置获取和地址解析功能';
      case 'device':
        return '测试设备标识和信息获取功能';
      case 'system':
        return '测试提示框、震动、分享等系统功能';
      case 'titlebar':
        return '测试标题栏动态控制功能';
      default:
        return '设备功能测试';
    }
  }

  /// 获取功能图标
  static IconData getFunctionIcon(String function) {
    switch (function) {
      case 'camera':
        return Icons.camera_alt;
      case 'qrcode':
        return Icons.qr_code_scanner;
      case 'bluetooth':
        return Icons.bluetooth;
      case 'nfc':
        return Icons.nfc;
      case 'location':
        return Icons.location_on;
      case 'device':
        return Icons.phone_android;
      case 'system':
        return Icons.settings;
      case 'titlebar':
        return Icons.title;
      default:
        return Icons.dashboard;
    }
  }
}
