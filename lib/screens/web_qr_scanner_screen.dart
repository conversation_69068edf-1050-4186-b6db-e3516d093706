import 'package:flutter/material.dart';
import '../constants/app_theme.dart';
import '../services/localization_service.dart';

class WebQRScannerScreen extends StatefulWidget {
  const WebQRScannerScreen({super.key});

  @override
  State<WebQRScannerScreen> createState() => _WebQRScannerScreenState();
}

class _WebQRScannerScreenState extends State<WebQRScannerScreen> {
  String? result;



  void _confirmResult() {
    Navigator.of(context).pop(result);
  }

  void _continueScan() {
    setState(() {
      result = null;
    });
  }

  void _manualInput() {
    _showManualInputDialog();
  }

  void _showManualInputDialog() {
    final controller = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(LocalizationService.t('manual_input')),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(LocalizationService.t('qr_input_hint')),
            const SizedBox(height: 16),
            TextField(
              controller: controller,
              decoration: InputDecoration(
                hintText: LocalizationService.t('qr_example'),
                border: const OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(LocalizationService.t('cancel')),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              if (controller.text.isNotEmpty) {
                setState(() {
                  result = controller.text;
                });
              }
            },
            child: Text(LocalizationService.t('confirm')),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: Text(LocalizationService.t('scan_qr_code')),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _manualInput,
            icon: const Icon(Icons.keyboard),
            tooltip: LocalizationService.t('manual_input'),
          ),
        ],
      ),
      body: result != null ? _buildResultView() : _buildScannerView(),
    );
  }

  Widget _buildScannerView() {
    return Stack(
      children: [
        // 模拟相机预览区域
        Positioned.fill(
          child: Container(
            color: Colors.black,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.camera_alt,
                    color: Colors.white54,
                    size: 80,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    '浏览器二维码扫描\n(需要相机权限)',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  Text(
                    '在实际应用中，这里会集成\nJavaScript二维码扫描库',
                    style: TextStyle(
                      color: Colors.white54,
                      fontSize: 12,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
        // 扫描框
        Positioned.fill(
          child: CustomPaint(
            painter: QRScannerOverlayPainter(),
          ),
        ),
        // 底部提示
        Positioned(
          bottom: 100,
          left: 0,
          right: 0,
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 20),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.black54,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              LocalizationService.t('scan_instruction'),
              style: const TextStyle(color: Colors.white, fontSize: 16),
              textAlign: TextAlign.center,
            ),
          ),
        ),
        // 功能按钮
        Positioned(
          bottom: 20,
          left: 0,
          right: 0,
          child: Center(
            child: ElevatedButton.icon(
              onPressed: _manualInput,
              icon: const Icon(Icons.keyboard),
              label: Text(LocalizationService.t('manual_input')),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: Colors.black,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildResultView() {
    return Container(
      color: Colors.black,
      child: Column(
        children: [
          Expanded(
            child: Center(
              child: Container(
                margin: const EdgeInsets.all(20),
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.check_circle,
                      color: AppTheme.successColor,
                      size: 64,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      LocalizationService.t('scan_success'),
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        result!,
                        style: const TextStyle(
                          fontFamily: 'monospace',
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: _continueScan,
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.white,
                      side: const BorderSide(color: Colors.white),
                    ),
                    child: Text(LocalizationService.t('continue_scan')),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _confirmResult,
                    child: Text(LocalizationService.t('use_result')),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class QRScannerOverlayPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.black54
      ..style = PaintingStyle.fill;

    final scanAreaSize = size.width * 0.7;
    final scanAreaLeft = (size.width - scanAreaSize) / 2;
    final scanAreaTop = (size.height - scanAreaSize) / 2;

    // 绘制遮罩
    canvas.drawPath(
      Path()
        ..addRect(Rect.fromLTWH(0, 0, size.width, size.height))
        ..addRect(Rect.fromLTWH(scanAreaLeft, scanAreaTop, scanAreaSize, scanAreaSize))
        ..fillType = PathFillType.evenOdd,
      paint,
    );

    // 绘制扫描框边角
    final cornerPaint = Paint()
      ..color = AppTheme.primaryColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 4;

    final cornerLength = 20.0;

    // 左上角
    canvas.drawPath(
      Path()
        ..moveTo(scanAreaLeft, scanAreaTop + cornerLength)
        ..lineTo(scanAreaLeft, scanAreaTop)
        ..lineTo(scanAreaLeft + cornerLength, scanAreaTop),
      cornerPaint,
    );

    // 右上角
    canvas.drawPath(
      Path()
        ..moveTo(scanAreaLeft + scanAreaSize - cornerLength, scanAreaTop)
        ..lineTo(scanAreaLeft + scanAreaSize, scanAreaTop)
        ..lineTo(scanAreaLeft + scanAreaSize, scanAreaTop + cornerLength),
      cornerPaint,
    );

    // 左下角
    canvas.drawPath(
      Path()
        ..moveTo(scanAreaLeft, scanAreaTop + scanAreaSize - cornerLength)
        ..lineTo(scanAreaLeft, scanAreaTop + scanAreaSize)
        ..lineTo(scanAreaLeft + cornerLength, scanAreaTop + scanAreaSize),
      cornerPaint,
    );

    // 右下角
    canvas.drawPath(
      Path()
        ..moveTo(scanAreaLeft + scanAreaSize - cornerLength, scanAreaTop + scanAreaSize)
        ..lineTo(scanAreaLeft + scanAreaSize, scanAreaTop + scanAreaSize)
        ..lineTo(scanAreaLeft + scanAreaSize, scanAreaTop + scanAreaSize - cornerLength),
      cornerPaint,
    );
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}