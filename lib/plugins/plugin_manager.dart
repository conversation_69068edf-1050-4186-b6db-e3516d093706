import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'native_plugin.dart';

/// 插件管理器
/// 负责管理所有WebView原生功能插件的注册、执行和生命周期
class PluginManager {
  static final PluginManager _instance = PluginManager._internal();
  factory PluginManager() => _instance;
  PluginManager._internal();
  
  /// 获取单例实例
  static PluginManager get instance => _instance;
  
  /// 已注册的插件映射
  final Map<String, NativePlugin> _plugins = {};
  
  /// 插件初始化状态
  final Map<String, bool> _pluginInitialized = {};
  
  /// 是否已初始化
  bool _initialized = false;
  
  /// 注册插件
  /// [plugin] 要注册的插件实例
  void registerPlugin(NativePlugin plugin) {
    if (_plugins.containsKey(plugin.name)) {
      debugPrint('PluginManager: 插件 ${plugin.name} 已存在，将被覆盖');
    }
    
    _plugins[plugin.name] = plugin;
    _pluginInitialized[plugin.name] = false;
    
    debugPrint('PluginManager: 注册插件 ${plugin.name}，支持方法: ${plugin.methods}');
  }
  
  /// 注销插件
  /// [pluginName] 插件名称
  Future<void> unregisterPlugin(String pluginName) async {
    final plugin = _plugins[pluginName];
    if (plugin != null) {
      await plugin.dispose();
      _plugins.remove(pluginName);
      _pluginInitialized.remove(pluginName);
      debugPrint('PluginManager: 注销插件 $pluginName');
    }
  }
  
  /// 获取插件实例
  /// [pluginName] 插件名称
  NativePlugin? getPlugin(String pluginName) {
    return _plugins[pluginName];
  }
  
  /// 检查插件是否存在
  /// [pluginName] 插件名称
  bool hasPlugin(String pluginName) {
    return _plugins.containsKey(pluginName);
  }
  
  /// 执行插件方法
  /// [method] 方法名，格式为 "pluginName.methodName"
  /// [params] 参数Map
  /// 返回执行结果
  Future<dynamic> executeMethod(String method, Map<String, dynamic> params) async {
    try {
      // 解析方法名
      final parts = method.split('.');
      if (parts.length != 2) {
        throw PluginException(
          code: PluginErrorCodes.methodNotSupported,
          message: '方法名格式错误，应为 "pluginName.methodName": $method',
          pluginName: 'PluginManager',
        );
      }
      
      final pluginName = parts[0];
      final methodName = parts[1];
      
      // 检查插件是否存在
      final plugin = _plugins[pluginName];
      if (plugin == null) {
        throw PluginException(
          code: PluginErrorCodes.featureNotSupported,
          message: '插件不存在: $pluginName',
          pluginName: 'PluginManager',
        );
      }
      
      // 确保插件已初始化
      if (!_pluginInitialized[pluginName]!) {
        await plugin.initialize();
        _pluginInitialized[pluginName] = true;
      }
      
      // 执行插件方法
      debugPrint('PluginManager: 执行方法 $method，参数: $params');
      final result = await plugin.execute(methodName, params);
      debugPrint('PluginManager: 方法 $method 执行成功');
      
      return result;
      
    } on PluginException {
      rethrow;
    } catch (e) {
      debugPrint('PluginManager: 执行方法 $method 失败: $e');
      throw PluginException(
        code: PluginErrorCodes.executionError,
        message: '方法执行失败: ${e.toString()}',
        pluginName: 'PluginManager',
        details: {'method': method, 'params': params, 'error': e.toString()},
      );
    }
  }
  
  /// 获取所有插件信息
  /// 返回插件名称到方法列表的映射
  Map<String, List<String>> getAllPlugins() {
    return _plugins.map((name, plugin) => MapEntry(name, plugin.methods));
  }
  
  /// 获取所有插件的权限状态
  Future<Map<String, Map<String, dynamic>>> getAllPermissionStatuses() async {
    final Map<String, Map<String, dynamic>> result = {};
    
    for (final entry in _plugins.entries) {
      final pluginName = entry.key;
      final plugin = entry.value;
      
      result[pluginName] = {
        'permissions': plugin.permissions.map((p) => p.toString()).toList(),
        'statuses': await plugin.getPermissionStatuses(),
        'hasPermissions': await plugin.checkPermissions(),
      };
    }
    
    return result;
  }
  
  /// 请求所有插件权限
  Future<Map<String, bool>> requestAllPermissions() async {
    final Map<String, bool> result = {};
    
    for (final entry in _plugins.entries) {
      final pluginName = entry.key;
      final plugin = entry.value;
      
      if (plugin.permissions.isNotEmpty) {
        result[pluginName] = await plugin.requestPermissions();
      } else {
        result[pluginName] = true;
      }
    }
    
    return result;
  }
  
  /// 初始化所有插件
  Future<void> initializeAllPlugins() async {
    if (_initialized) return;
    
    debugPrint('PluginManager: 开始初始化所有插件');
    
    for (final entry in _plugins.entries) {
      final pluginName = entry.key;
      final plugin = entry.value;
      
      try {
        await plugin.initialize();
        _pluginInitialized[pluginName] = true;
        debugPrint('PluginManager: 插件 $pluginName 初始化成功');
      } catch (e) {
        debugPrint('PluginManager: 插件 $pluginName 初始化失败: $e');
        _pluginInitialized[pluginName] = false;
      }
    }
    
    _initialized = true;
    debugPrint('PluginManager: 所有插件初始化完成');
  }
  
  /// 销毁所有插件
  Future<void> disposeAllPlugins() async {
    debugPrint('PluginManager: 开始销毁所有插件');
    
    for (final entry in _plugins.entries) {
      final pluginName = entry.key;
      final plugin = entry.value;
      
      try {
        await plugin.dispose();
        debugPrint('PluginManager: 插件 $pluginName 销毁成功');
      } catch (e) {
        debugPrint('PluginManager: 插件 $pluginName 销毁失败: $e');
      }
    }
    
    _pluginInitialized.clear();
    _initialized = false;
    debugPrint('PluginManager: 所有插件销毁完成');
  }
  
  /// 获取插件统计信息
  Map<String, dynamic> getStatistics() {
    return {
      'totalPlugins': _plugins.length,
      'initializedPlugins': _pluginInitialized.values.where((v) => v).length,
      'pluginNames': _plugins.keys.toList(),
      'totalMethods': _plugins.values.fold<int>(
        0, 
        (sum, plugin) => sum + plugin.methods.length
      ),
    };
  }
  
  /// 验证插件配置
  List<String> validatePlugins() {
    final List<String> issues = [];
    
    for (final entry in _plugins.entries) {
      final pluginName = entry.key;
      final plugin = entry.value;
      
      // 检查插件名称是否与实际名称一致
      if (plugin.name != pluginName) {
        issues.add('插件 $pluginName 的名称不一致: ${plugin.name}');
      }
      
      // 检查方法列表是否为空
      if (plugin.methods.isEmpty) {
        issues.add('插件 $pluginName 没有定义任何方法');
      }
      
      // 检查方法名称是否重复
      final methodSet = plugin.methods.toSet();
      if (methodSet.length != plugin.methods.length) {
        issues.add('插件 $pluginName 存在重复的方法名');
      }
    }
    
    return issues;
  }
  
  /// 重新加载插件
  Future<void> reloadPlugin(String pluginName) async {
    final plugin = _plugins[pluginName];
    if (plugin != null) {
      // 先销毁
      await plugin.dispose();
      _pluginInitialized[pluginName] = false;
      
      // 重新初始化
      await plugin.initialize();
      _pluginInitialized[pluginName] = true;
      
      debugPrint('PluginManager: 插件 $pluginName 重新加载完成');
    }
  }
  
  /// 获取插件健康状态
  Future<Map<String, Map<String, dynamic>>> getPluginHealthStatus() async {
    final Map<String, Map<String, dynamic>> result = {};
    
    for (final entry in _plugins.entries) {
      final pluginName = entry.key;
      final plugin = entry.value;
      
      result[pluginName] = {
        'initialized': _pluginInitialized[pluginName] ?? false,
        'hasPermissions': await plugin.checkPermissions(),
        'methodCount': plugin.methods.length,
        'permissionCount': plugin.permissions.length,
        'status': 'healthy', // 可以根据实际情况扩展健康检查逻辑
      };
    }
    
    return result;
  }
}
