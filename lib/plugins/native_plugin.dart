import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';

/// 原生插件接口定义
/// 所有WebView原生功能插件都必须实现此接口
abstract class NativePlugin {
  /// 插件名称，用于标识和路由
  String get name;
  
  /// 插件支持的方法列表
  List<String> get methods;
  
  /// 插件需要的权限列表
  List<Permission> get permissions => [];
  
  /// 执行插件方法
  /// [method] 方法名
  /// [params] 参数Map
  /// 返回执行结果，失败时抛出异常
  Future<dynamic> execute(String method, Map<String, dynamic> params);
  
  /// 检查插件所需权限是否已授予
  Future<bool> checkPermissions() async {
    if (permissions.isEmpty) return true;
    
    for (final permission in permissions) {
      final status = await permission.status;
      if (!status.isGranted) {
        return false;
      }
    }
    return true;
  }
  
  /// 请求插件所需权限
  Future<bool> requestPermissions() async {
    if (permissions.isEmpty) return true;
    
    final Map<Permission, PermissionStatus> statuses = 
        await permissions.request();
    
    return statuses.values.every((status) => status.isGranted);
  }
  
  /// 获取权限状态详情
  Future<Map<String, PermissionStatus>> getPermissionStatuses() async {
    final Map<String, PermissionStatus> result = {};
    
    for (final permission in permissions) {
      result[permission.toString()] = await permission.status;
    }
    
    return result;
  }
  
  /// 插件初始化（可选重写）
  Future<void> initialize() async {}
  
  /// 插件销毁（可选重写）
  Future<void> dispose() async {}
  
  /// 验证方法是否支持
  bool isMethodSupported(String method) {
    return methods.contains(method);
  }
  
  /// 验证参数（可选重写）
  void validateParams(String method, Map<String, dynamic> params) {
    // 子类可以重写此方法进行参数验证
  }
  
  /// 处理权限被拒绝的情况
  Future<void> handlePermissionDenied(Permission permission) async {
    debugPrint('${name}Plugin: 权限被拒绝 - $permission');
    
    // 可以在这里显示权限说明对话框
    // 或者引导用户到设置页面
  }
  
  /// 创建标准化的成功响应
  Map<String, dynamic> createSuccessResponse(dynamic data) {
    return {
      'success': true,
      'data': data,
      'timestamp': DateTime.now().toIso8601String(),
      'plugin': name,
    };
  }
  
  /// 创建标准化的错误响应
  Map<String, dynamic> createErrorResponse(String code, String message, [dynamic details]) {
    return {
      'success': false,
      'error': {
        'code': code,
        'message': message,
        'details': details,
        'plugin': name,
        'timestamp': DateTime.now().toIso8601String(),
      }
    };
  }
}

/// 插件异常类
class PluginException implements Exception {
  final String code;
  final String message;
  final dynamic details;
  final String pluginName;
  
  const PluginException({
    required this.code,
    required this.message,
    required this.pluginName,
    this.details,
  });
  
  @override
  String toString() {
    return 'PluginException($pluginName): [$code] $message';
  }
  
  Map<String, dynamic> toMap() {
    return {
      'code': code,
      'message': message,
      'details': details,
      'plugin': pluginName,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
}

/// 权限异常类
class PermissionException extends PluginException {
  final Permission permission;
  
  const PermissionException({
    required this.permission,
    required String pluginName,
    String? message,
  }) : super(
    code: 'PERMISSION_DENIED',
    message: message ?? '权限被拒绝: $permission',
    pluginName: pluginName,
    details: {'permission': permission},
  );
}

/// 方法不支持异常类
class MethodNotSupportedException extends PluginException {
  final String method;
  
  const MethodNotSupportedException({
    required this.method,
    required String pluginName,
  }) : super(
    code: 'METHOD_NOT_SUPPORTED',
    message: '方法不支持: $method',
    pluginName: pluginName,
    details: {'method': method},
  );
}

/// 参数验证异常类
class ParameterValidationException extends PluginException {
  final String parameter;
  
  const ParameterValidationException({
    required this.parameter,
    required String message,
    required String pluginName,
  }) : super(
    code: 'PARAMETER_VALIDATION_ERROR',
    message: message,
    pluginName: pluginName,
    details: {'parameter': parameter},
  );
}

/// 插件基类，提供通用功能实现
abstract class BaseNativePlugin implements NativePlugin {
  bool _initialized = false;
  
  /// 是否已初始化
  bool get isInitialized => _initialized;
  
  @override
  Future<dynamic> execute(String method, Map<String, dynamic> params) async {
    try {
      // 检查插件是否已初始化
      if (!_initialized) {
        await initialize();
        _initialized = true;
      }
      
      // 验证方法是否支持
      if (!isMethodSupported(method)) {
        throw MethodNotSupportedException(
          method: method,
          pluginName: name,
        );
      }
      
      // 验证参数
      validateParams(method, params);
      
      // 检查权限
      if (!await checkPermissions()) {
        final granted = await requestPermissions();
        if (!granted) {
          throw PermissionException(
            permission: permissions.first, // 简化处理，实际可能需要更详细的权限信息
            pluginName: name,
          );
        }
      }
      
      // 执行具体方法
      return await executeMethod(method, params);
      
    } on PluginException {
      rethrow;
    } catch (e) {
      throw PluginException(
        code: 'EXECUTION_ERROR',
        message: e.toString(),
        pluginName: name,
        details: {'originalError': e.toString()},
      );
    }
  }
  
  /// 子类需要实现的具体方法执行逻辑
  Future<dynamic> executeMethod(String method, Map<String, dynamic> params);
  
  @override
  Future<void> dispose() async {
    _initialized = false;
  }
}

/// 错误代码常量
class PluginErrorCodes {
  static const String unknownError = 'UNKNOWN_ERROR';
  static const String permissionDenied = 'PERMISSION_DENIED';
  static const String featureNotSupported = 'FEATURE_NOT_SUPPORTED';
  static const String timeout = 'TIMEOUT';
  static const String methodNotSupported = 'METHOD_NOT_SUPPORTED';
  static const String parameterValidationError = 'PARAMETER_VALIDATION_ERROR';
  static const String executionError = 'EXECUTION_ERROR';
  
  // 相机相关错误
  static const String cameraNotAvailable = 'CAMERA_NOT_AVAILABLE';
  static const String cameraPermissionDenied = 'CAMERA_PERMISSION_DENIED';
  
  // 蓝牙相关错误
  static const String bluetoothNotEnabled = 'BLUETOOTH_NOT_ENABLED';
  static const String bluetoothDeviceNotFound = 'BLUETOOTH_DEVICE_NOT_FOUND';
  static const String bluetoothConnectionFailed = 'BLUETOOTH_CONNECTION_FAILED';
  
  // NFC相关错误
  static const String nfcNotAvailable = 'NFC_NOT_AVAILABLE';
  static const String nfcDisabled = 'NFC_DISABLED';
  static const String nfcTagNotFound = 'NFC_TAG_NOT_FOUND';
  
  // 定位相关错误
  static const String locationPermissionDenied = 'LOCATION_PERMISSION_DENIED';
  static const String locationServiceDisabled = 'LOCATION_SERVICE_DISABLED';
  static const String locationTimeout = 'LOCATION_TIMEOUT';
}
