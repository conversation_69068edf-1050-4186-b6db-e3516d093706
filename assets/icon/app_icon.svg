<?xml version="1.0" encoding="UTF-8"?>
<svg width="1024" height="1024" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="shellGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4A90E2;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#357ABD;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2E5B8A;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="webGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#50C878;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3A9B5C;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="deviceGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF6B35;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E55A2B;stop-opacity:1" />
    </linearGradient>
    
    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="4" dy="4" stdDeviation="8" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="512" cy="512" r="480" fill="url(#shellGradient)" filter="url(#shadow)"/>
  
  <!-- 外层装饰环 -->
  <circle cx="512" cy="512" r="440" fill="none" stroke="#FFFFFF" stroke-width="4" opacity="0.3"/>
  
  <!-- Shell 外壳图标 (代表App外壳) -->
  <g transform="translate(512, 300)">
    <!-- Shell主体 -->
    <path d="M -120 -80 Q -120 -120 -80 -120 L 80 -120 Q 120 -120 120 -80 L 120 80 Q 120 120 80 120 L -80 120 Q -120 120 -120 80 Z" 
          fill="url(#shellGradient)" stroke="#FFFFFF" stroke-width="6"/>
    
    <!-- Shell 顶部装饰线 -->
    <rect x="-100" y="-100" width="200" height="8" rx="4" fill="#FFFFFF" opacity="0.8"/>
    
    <!-- Shell 内部网格 (代表结构化) -->
    <g opacity="0.4">
      <line x1="-80" y1="-60" x2="80" y2="-60" stroke="#FFFFFF" stroke-width="2"/>
      <line x1="-80" y1="-20" x2="80" y2="-20" stroke="#FFFFFF" stroke-width="2"/>
      <line x1="-80" y1="20" x2="80" y2="20" stroke="#FFFFFF" stroke-width="2"/>
      <line x1="-80" y1="60" x2="80" y2="60" stroke="#FFFFFF" stroke-width="2"/>
      <line x1="-40" y1="-80" x2="-40" y2="80" stroke="#FFFFFF" stroke-width="2"/>
      <line x1="0" y1="-80" x2="0" y2="80" stroke="#FFFFFF" stroke-width="2"/>
      <line x1="40" y1="-80" x2="40" y2="80" stroke="#FFFFFF" stroke-width="2"/>
    </g>
  </g>
  
  <!-- WebView 浏览器图标 -->
  <g transform="translate(400, 550)">
    <rect x="-60" y="-40" width="120" height="80" rx="8" fill="url(#webGradient)" stroke="#FFFFFF" stroke-width="3"/>
    <!-- 地址栏 -->
    <rect x="-50" y="-30" width="100" height="12" rx="6" fill="#FFFFFF" opacity="0.9"/>
    <!-- 内容区域 -->
    <rect x="-50" y="-10" width="100" height="40" rx="4" fill="#FFFFFF" opacity="0.7"/>
    <!-- 浏览器按钮 -->
    <circle cx="-40" cy="-24" r="3" fill="#FF5F56"/>
    <circle cx="-30" cy="-24" r="3" fill="#FFBD2E"/>
    <circle cx="-20" cy="-24" r="3" fill="#27CA3F"/>
  </g>
  
  <!-- 设备功能图标组 -->
  <g transform="translate(624, 550)">
    <!-- 手机设备轮廓 -->
    <rect x="-35" y="-50" width="70" height="100" rx="12" fill="url(#deviceGradient)" stroke="#FFFFFF" stroke-width="3"/>
    <!-- 屏幕 -->
    <rect x="-25" y="-35" width="50" height="70" rx="4" fill="#FFFFFF" opacity="0.9"/>
    
    <!-- 设备功能图标 -->
    <!-- 相机图标 -->
    <g transform="translate(-15, -20)">
      <rect x="-6" y="-4" width="12" height="8" rx="2" fill="#4A90E2"/>
      <circle cx="0" cy="0" r="3" fill="#FFFFFF"/>
    </g>
    
    <!-- 蓝牙图标 -->
    <g transform="translate(0, -5)">
      <path d="M -3 -6 L 3 0 L -3 6 M 0 -6 L 0 6 M 0 0 L 3 -6" 
            stroke="#4A90E2" stroke-width="1.5" fill="none"/>
    </g>
    
    <!-- GPS图标 -->
    <g transform="translate(15, -20)">
      <circle cx="0" cy="0" r="4" fill="none" stroke="#4A90E2" stroke-width="1.5"/>
      <circle cx="0" cy="0" r="1.5" fill="#4A90E2"/>
    </g>
    
    <!-- NFC图标 -->
    <g transform="translate(-10, 15)">
      <path d="M -4 -3 Q 0 -3 0 0 Q 0 3 4 3" stroke="#4A90E2" stroke-width="1.5" fill="none"/>
      <path d="M -2 -1 Q 0 -1 0 0 Q 0 1 2 1" stroke="#4A90E2" stroke-width="1.5" fill="none"/>
    </g>
    
    <!-- 扫码图标 -->
    <g transform="translate(10, 15)">
      <rect x="-4" y="-3" width="8" height="6" fill="none" stroke="#4A90E2" stroke-width="1"/>
      <line x1="-2" y1="-1" x2="-2" y2="1" stroke="#4A90E2" stroke-width="1"/>
      <line x1="0" y1="-1" x2="0" y2="1" stroke="#4A90E2" stroke-width="1"/>
      <line x1="2" y1="-1" x2="2" y2="1" stroke="#4A90E2" stroke-width="1"/>
    </g>
  </g>
  
  <!-- 连接线 (表示WebView与设备功能的连接) -->
  <g opacity="0.6">
    <!-- 从WebView到设备的连接线 -->
    <path d="M 460 550 Q 512 520 584 550" stroke="#FFFFFF" stroke-width="4" fill="none" stroke-dasharray="8,4"/>
    
    <!-- 从Shell到WebView的连接线 -->
    <path d="M 450 420 Q 420 480 400 510" stroke="#FFFFFF" stroke-width="4" fill="none" stroke-dasharray="8,4"/>
    
    <!-- 从Shell到设备的连接线 -->
    <path d="M 574 420 Q 604 480 624 510" stroke="#FFFFFF" stroke-width="4" fill="none" stroke-dasharray="8,4"/>
  </g>
  
  <!-- 中心标题文字 -->
  <g transform="translate(512, 750)">
    <text x="0" y="0" text-anchor="middle" font-family="Arial, sans-serif" font-size="48" font-weight="bold" fill="#FFFFFF">
      shell4app
    </text>
    <text x="0" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" fill="#FFFFFF" opacity="0.8">
      WebView + Device
    </text>
  </g>
  
  <!-- 装饰性光效 -->
  <g opacity="0.3">
    <circle cx="300" cy="200" r="20" fill="#FFFFFF"/>
    <circle cx="750" cy="300" r="15" fill="#FFFFFF"/>
    <circle cx="200" cy="700" r="12" fill="#FFFFFF"/>
    <circle cx="800" cy="750" r="18" fill="#FFFFFF"/>
  </g>
</svg>
