<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>强制权限请求测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 24px;
            margin-bottom: 8px;
        }
        
        .content {
            padding: 30px 20px;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .warning h3 {
            color: #856404;
            margin-bottom: 10px;
        }
        
        .permission-section {
            margin-bottom: 25px;
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            background: #f8f9fa;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .force-btn {
            background: #ff6b6b;
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            width: 100%;
            margin: 5px 0;
        }
        
        .force-btn:hover {
            background: #ee5a24;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
        }
        
        .force-btn:active {
            transform: translateY(0);
        }
        
        .result {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #ff6b6b;
        }
        
        .success {
            color: #28a745;
            border-color: #28a745;
        }
        
        .error {
            color: #dc3545;
            border-color: #dc3545;
        }
        
        .step {
            margin: 8px 0;
            padding-left: 20px;
            position: relative;
        }
        
        .step::before {
            content: "⚠️";
            position: absolute;
            left: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚡ 强制权限请求</h1>
            <p>立即弹出系统权限对话框</p>
        </div>
        
        <div class="content">
            <div class="warning">
                <h3>⚠️ 重要说明</h3>
                <div class="step">此页面会立即弹出系统权限对话框</div>
                <div class="step">请在弹出的对话框中点击"允许"</div>
                <div class="step">如果没有弹出对话框，说明权限已被永久拒绝</div>
                <div class="step">永久拒绝的权限需要到系统设置中手动开启</div>
            </div>
            
            <!-- 相机权限强制请求 -->
            <div class="permission-section">
                <div class="section-title">
                    📷 相机权限强制请求
                </div>
                <button class="force-btn" onclick="forceRequestCamera()">
                    🚀 立即弹出相机权限对话框
                </button>
                <div id="camera-result" class="result" style="display: none;"></div>
            </div>
            
            <!-- 相册权限强制请求 -->
            <div class="permission-section">
                <div class="section-title">
                    🖼️ 相册权限强制请求
                </div>
                <button class="force-btn" onclick="forceRequestPhotos()">
                    🚀 立即弹出相册权限对话框
                </button>
                <div id="photos-result" class="result" style="display: none;"></div>
            </div>
            
            <!-- 麦克风权限强制请求 -->
            <div class="permission-section">
                <div class="section-title">
                    🎤 麦克风权限强制请求
                </div>
                <button class="force-btn" onclick="forceRequestMicrophone()">
                    🚀 立即弹出麦克风权限对话框
                </button>
                <div id="microphone-result" class="result" style="display: none;"></div>
            </div>
            
            <!-- 蓝牙权限强制请求 -->
            <div class="permission-section">
                <div class="section-title">
                    🔵 蓝牙权限强制请求
                </div>
                <button class="force-btn" onclick="forceRequestBluetooth()">
                    🚀 立即弹出蓝牙权限对话框
                </button>
                <div id="bluetooth-result" class="result" style="display: none;"></div>
            </div>
            
            <!-- 全部权限一次性请求 -->
            <div class="permission-section">
                <div class="section-title">
                    🎯 全部权限一次性请求
                </div>
                <button class="force-btn" onclick="forceRequestAll()" style="background: #2d3436; font-size: 18px; padding: 20px;">
                    💥 一次性请求所有权限
                </button>
                <div id="all-result" class="result" style="display: none;"></div>
            </div>
            
            <div class="loading" id="loading">
                <div>⏳ 正在请求权限...</div>
            </div>
        </div>
    </div>

    <script>
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }
        
        function showResult(sectionId, message, isError = false) {
            const resultDiv = document.getElementById(sectionId + '-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result ' + (isError ? 'error' : 'success');
            resultDiv.textContent = message;
            showLoading(false);
        }
        
        // 强制请求相机权限
        async function forceRequestCamera() {
            showLoading(true);
            
            try {
                // 直接尝试拍照，这会强制弹出权限对话框
                const result = await window.NativeAPI.Camera.takePhoto({
                    quality: 80,
                    maxWidth: 1024,
                    maxHeight: 1024
                });
                
                showResult('camera', '✅ 相机权限请求成功！\n\n权限已授予，可以正常使用相机功能。\n\n拍照结果:\n' + JSON.stringify(result, null, 2));
            } catch (error) {
                let message = '❌ 相机权限请求失败\n\n';
                message += '错误信息: ' + error.message + '\n\n';
                
                if (error.message.includes('永久拒绝')) {
                    message += '解决方案:\n';
                    message += '1. 打开 设置 > 隐私与安全性 > 相机\n';
                    message += '2. 找到本应用并开启权限\n';
                    message += '3. 重新打开应用测试';
                } else if (error.message.includes('被拒绝')) {
                    message += '解决方案:\n';
                    message += '1. 重新点击按钮\n';
                    message += '2. 在弹出的对话框中点击"允许"\n';
                    message += '3. 如果没有弹出对话框，请到设置中手动开启';
                }
                
                showResult('camera', message, true);
            }
        }
        
        // 强制请求相册权限
        async function forceRequestPhotos() {
            showLoading(true);
            
            try {
                const result = await window.NativeAPI.Camera.pickFromGallery({
                    type: 'image',
                    quality: 80
                });
                
                showResult('photos', '✅ 相册权限请求成功！\n\n权限已授予，可以正常访问相册。\n\n选择结果:\n' + JSON.stringify(result, null, 2));
            } catch (error) {
                let message = '❌ 相册权限请求失败\n\n';
                message += '错误信息: ' + error.message + '\n\n';
                
                if (error.message.includes('永久拒绝')) {
                    message += '解决方案:\n';
                    message += '1. 打开 设置 > 隐私与安全性 > 照片\n';
                    message += '2. 找到本应用并选择"所有照片"\n';
                    message += '3. 重新打开应用测试';
                } else if (error.message.includes('被拒绝')) {
                    message += '解决方案:\n';
                    message += '1. 重新点击按钮\n';
                    message += '2. 在弹出的对话框中点击"允许"\n';
                    message += '3. 选择合适的权限级别';
                }
                
                showResult('photos', message, true);
            }
        }
        
        // 强制请求麦克风权限
        async function forceRequestMicrophone() {
            showLoading(true);
            
            try {
                const result = await window.NativeAPI.Camera.recordVideo({
                    maxDuration: 5
                });
                
                showResult('microphone', '✅ 麦克风权限请求成功！\n\n权限已授予，可以正常录制视频。\n\n录制结果:\n' + JSON.stringify(result, null, 2));
            } catch (error) {
                let message = '❌ 麦克风权限请求失败\n\n';
                message += '错误信息: ' + error.message + '\n\n';
                
                if (error.message.includes('永久拒绝')) {
                    message += '解决方案:\n';
                    message += '1. 打开 设置 > 隐私与安全性 > 麦克风\n';
                    message += '2. 找到本应用并开启权限\n';
                    message += '3. 重新打开应用测试';
                } else if (error.message.includes('被拒绝')) {
                    message += '解决方案:\n';
                    message += '1. 重新点击按钮\n';
                    message += '2. 在弹出的对话框中点击"允许"\n';
                    message += '3. 确保同时授予相机和麦克风权限';
                }
                
                showResult('microphone', message, true);
            }
        }
        
        // 强制请求蓝牙权限
        async function forceRequestBluetooth() {
            showLoading(true);
            
            try {
                const result = await window.NativeAPI.Bluetooth.scanDevices({
                    timeout: 5000
                });
                
                showResult('bluetooth', '✅ 蓝牙权限请求成功！\n\n权限已授予，可以正常扫描蓝牙设备。\n\n扫描结果:\n' + JSON.stringify(result, null, 2));
            } catch (error) {
                let message = '❌ 蓝牙权限请求失败\n\n';
                message += '错误信息: ' + error.message + '\n\n';
                
                if (error.message.includes('永久拒绝')) {
                    message += '解决方案:\n';
                    message += '1. 打开 设置 > 隐私与安全性 > 蓝牙\n';
                    message += '2. 找到本应用并开启权限\n';
                    message += '3. 确保蓝牙功能已开启\n';
                    message += '4. 重新打开应用测试';
                } else if (error.message.includes('被拒绝')) {
                    message += '解决方案:\n';
                    message += '1. 重新点击按钮\n';
                    message += '2. 在弹出的对话框中点击"允许"\n';
                    message += '3. 确保蓝牙功能已开启';
                } else if (error.message.includes('未开启')) {
                    message += '解决方案:\n';
                    message += '1. 打开蓝牙功能\n';
                    message += '2. 设置 > 蓝牙 或控制中心\n';
                    message += '3. 重新测试权限';
                }
                
                showResult('bluetooth', message, true);
            }
        }
        
        // 一次性请求所有权限
        async function forceRequestAll() {
            showLoading(true);
            
            const results = {
                camera: null,
                photos: null,
                microphone: null,
                bluetooth: null
            };
            
            let message = '🎯 全部权限请求结果:\n\n';
            
            // 测试相机权限
            try {
                await window.NativeAPI.Camera.takePhoto({ quality: 50, maxWidth: 512, maxHeight: 512 });
                results.camera = '✅ 成功';
            } catch (error) {
                results.camera = '❌ 失败: ' + error.message;
            }
            
            // 测试相册权限
            try {
                await window.NativeAPI.Camera.pickFromGallery({ type: 'image', quality: 50 });
                results.photos = '✅ 成功';
            } catch (error) {
                results.photos = '❌ 失败: ' + error.message;
            }
            
            // 测试麦克风权限（通过录像）
            try {
                await window.NativeAPI.Camera.recordVideo({ maxDuration: 3 });
                results.microphone = '✅ 成功';
            } catch (error) {
                results.microphone = '❌ 失败: ' + error.message;
            }
            
            // 测试蓝牙权限
            try {
                await window.NativeAPI.Bluetooth.scanDevices({ timeout: 3000 });
                results.bluetooth = '✅ 成功';
            } catch (error) {
                results.bluetooth = '❌ 失败: ' + error.message;
            }
            
            message += '📷 相机权限: ' + results.camera + '\n';
            message += '🖼️ 相册权限: ' + results.photos + '\n';
            message += '🎤 麦克风权限: ' + results.microphone + '\n';
            message += '🔵 蓝牙权限: ' + results.bluetooth + '\n\n';
            
            const successCount = Object.values(results).filter(r => r && r.includes('成功')).length;
            const totalCount = Object.keys(results).length;
            
            message += `📊 权限授予情况: ${successCount}/${totalCount}\n\n`;
            
            if (successCount === totalCount) {
                message += '🎉 恭喜！所有权限都已成功授予！\n';
                message += '现在可以正常使用所有设备功能了。';
            } else {
                message += '⚠️ 部分权限未授予，请:\n';
                message += '1. 检查上述失败的权限\n';
                message += '2. 到系统设置中手动开启\n';
                message += '3. 重新测试功能';
            }
            
            showResult('all', message, successCount < totalCount);
        }
    </script>
</body>
</html>
