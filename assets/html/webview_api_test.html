<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebView API 测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 24px;
            margin-bottom: 8px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 14px;
        }
        
        .section {
            padding: 20px;
            border-bottom: 1px solid #eee;
        }
        
        .section:last-child {
            border-bottom: none;
        }

        .section.highlighted {
            background: linear-gradient(135deg, #fff3e0 0%, #ffffff 100%);
            border-left: 4px solid #ff9800;
            animation: highlight-pulse 2s ease-in-out;
        }

        @keyframes highlight-pulse {
            0% { box-shadow: 0 0 0 0 rgba(255, 152, 0, 0.4); }
            50% { box-shadow: 0 0 20px 10px rgba(255, 152, 0, 0.2); }
            100% { box-shadow: 0 0 0 0 rgba(255, 152, 0, 0); }
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .section-title::before {
            content: '';
            width: 4px;
            height: 18px;
            background: #667eea;
            margin-right: 10px;
            border-radius: 2px;
        }
        
        .button-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            text-align: center;
        }
        
        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }
        
        .btn:active {
            transform: translateY(0);
        }
        
        .btn.secondary {
            background: #6c757d;
        }
        
        .btn.secondary:hover {
            background: #5a6268;
        }
        
        .btn.success {
            background: #28a745;
        }
        
        .btn.success:hover {
            background: #218838;
        }
        
        .btn.danger {
            background: #dc3545;
        }
        
        .btn.danger:hover {
            background: #c82333;
        }
        
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 12px;
            margin-top: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-break: break-all;
        }
        
        .result.success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        
        .result.error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .loading.show {
            display: block;
        }
        
        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .status-bar {
            background: #e9ecef;
            padding: 10px 20px;
            font-size: 12px;
            color: #6c757d;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 5px;
        }
        
        .status-indicator.online {
            background: #28a745;
        }
        
        .status-indicator.offline {
            background: #dc3545;
        }
        
        .input-group {
            margin-bottom: 10px;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }
        
        .input-group input,
        .input-group select,
        .input-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .input-group textarea {
            resize: vertical;
            min-height: 60px;
        }
        
        .image-preview {
            max-width: 100%;
            max-height: 200px;
            border-radius: 4px;
            margin-top: 10px;
        }

        .quick-nav {
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            padding: 15px 20px;
        }

        .nav-title {
            font-size: 14px;
            font-weight: 600;
            color: #495057;
            margin-bottom: 10px;
        }

        .nav-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .nav-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .nav-btn:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }

        .nav-btn:active {
            transform: translateY(0);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>WebView API 测试中心</h1>
            <p>测试所有原生功能接口</p>
        </div>
        
        <div class="status-bar">
            <div>
                <span class="status-indicator online"></span>
                API状态: 已连接
            </div>
            <div id="timestamp">加载时间: --</div>
        </div>

        <!-- 快速导航菜单 -->
        <div class="quick-nav" id="quickNav" style="display: none;">
            <div class="nav-title">快速导航</div>
            <div class="nav-buttons">
                <button class="nav-btn" onclick="scrollToSection('camera')">📷 相机</button>
                <button class="nav-btn" onclick="scrollToSection('qrcode')">📱 二维码</button>
                <button class="nav-btn" onclick="scrollToSection('bluetooth')">🔵 蓝牙</button>
                <button class="nav-btn" onclick="scrollToSection('nfc')">📡 NFC</button>
                <button class="nav-btn" onclick="scrollToSection('location')">📍 定位</button>
                <button class="nav-btn" onclick="scrollToSection('device')">📱 设备</button>
                <button class="nav-btn" onclick="scrollToSection('system')">⚙️ 系统</button>
                <button class="nav-btn" onclick="scrollToSection('titlebar')">📋 标题栏</button>
            </div>
        </div>
        
        <!-- 相机功能测试 -->
        <div class="section" id="camera">
            <div class="section-title">📷 相机功能</div>
            <div class="button-group">
                <button class="btn" onclick="testTakePhoto()">拍照</button>
                <button class="btn" onclick="testRecordVideo()">录像</button>
                <button class="btn" onclick="testPickFromGallery()">选择图片</button>
                <button class="btn" onclick="testPickMultiple()">多选图片</button>
            </div>
            <div class="input-group">
                <label>图片质量 (0-100):</label>
                <input type="number" id="imageQuality" value="80" min="0" max="100">
            </div>
            <div id="cameraResult" class="result"></div>
            <img id="imagePreview" class="image-preview" style="display: none;">
        </div>
        
        <!-- 二维码功能测试 -->
        <div class="section" id="qrcode">
            <div class="section-title">📱 二维码功能</div>
            <div class="button-group">
                <button class="btn" onclick="testQRScan()">扫描二维码</button>
                <button class="btn" onclick="testQRGenerate()">生成二维码</button>
                <button class="btn" onclick="testQRScanMultiple()">批量扫描</button>
            </div>
            <div class="input-group">
                <label>生成内容:</label>
                <input type="text" id="qrData" value="https://example.com" placeholder="输入要生成二维码的内容">
            </div>
            <div class="input-group">
                <label>二维码大小:</label>
                <input type="number" id="qrSize" value="200" min="100" max="500">
            </div>
            <div id="qrResult" class="result"></div>
            <img id="qrPreview" class="image-preview" style="display: none;">
        </div>
        
        <!-- 蓝牙功能测试 -->
        <div class="section" id="bluetooth">
            <div class="section-title">🔵 蓝牙功能</div>
            <div class="button-group">
                <button class="btn" onclick="testBluetoothScan()">扫描设备</button>
                <button class="btn secondary" onclick="testBluetoothConnect()">连接设备</button>
                <button class="btn danger" onclick="testBluetoothDisconnect()">断开连接</button>
            </div>
            <div class="button-group">
                <button class="btn success" onclick="testBluetoothPrintText()">打印文本</button>
                <button class="btn success" onclick="testBluetoothPrintQR()">打印二维码</button>
                <button class="btn success" onclick="testBluetoothPrintReceipt()">打印小票</button>
            </div>
            <div class="input-group">
                <label>设备ID:</label>
                <input type="text" id="bluetoothDeviceId" placeholder="选择设备后自动填入">
            </div>
            <div class="input-group">
                <label>打印文本:</label>
                <textarea id="printText" placeholder="输入要打印的文本">Hello World!</textarea>
            </div>
            <div id="bluetoothResult" class="result"></div>
        </div>
        
        <!-- NFC功能测试 -->
        <div class="section" id="nfc">
            <div class="section-title">📡 NFC功能</div>
            <div class="button-group">
                <button class="btn" onclick="testNFCAvailable()">检查可用性</button>
                <button class="btn" onclick="testNFCScan()">扫描标签</button>
                <button class="btn secondary" onclick="testNFCWrite()">写入标签</button>
            </div>
            <div class="input-group">
                <label>写入类型:</label>
                <select id="nfcType">
                    <option value="text">文本</option>
                    <option value="url">网址</option>
                    <option value="wifi">WiFi</option>
                </select>
            </div>
            <div class="input-group">
                <label>写入内容:</label>
                <input type="text" id="nfcData" value="Hello NFC!" placeholder="输入要写入的内容">
            </div>
            <div id="nfcResult" class="result"></div>
        </div>
        
        <!-- 定位功能测试 -->
        <div class="section" id="location">
            <div class="section-title">📍 定位功能</div>
            <div class="button-group">
                <button class="btn" onclick="testGetLocation()">获取位置</button>
                <button class="btn" onclick="testGeocode()">地址解析</button>
                <button class="btn secondary" onclick="testWatchLocation()">监听位置</button>
                <button class="btn danger" onclick="testStopWatch()">停止监听</button>
            </div>
            <div class="input-group">
                <label>地址:</label>
                <input type="text" id="address" value="北京市朝阳区" placeholder="输入地址进行解析">
            </div>
            <div id="locationResult" class="result"></div>
        </div>
        
        <!-- 设备信息测试 -->
        <div class="section" id="device">
            <div class="section-title">📱 设备信息</div>
            <div class="button-group">
                <button class="btn" onclick="testGetDeviceId()">设备ID</button>
                <button class="btn" onclick="testGetDeviceInfo()">设备信息</button>
                <button class="btn" onclick="testGetUserInfo()">用户信息</button>
                <button class="btn" onclick="testGetAppInfo()">应用信息</button>
            </div>
            <div id="deviceResult" class="result"></div>
        </div>
        
        <!-- 系统功能测试 -->
        <div class="section" id="system">
            <div class="section-title">⚙️ 系统功能</div>
            <div class="button-group">
                <button class="btn" onclick="testShowToast()">显示提示</button>
                <button class="btn" onclick="testShowAlert()">显示警告</button>
                <button class="btn" onclick="testShowConfirm()">确认对话框</button>
                <button class="btn secondary" onclick="testVibrate()">震动</button>
            </div>
            <div class="button-group">
                <button class="btn success" onclick="testOpenUrl()">打开网址</button>
                <button class="btn success" onclick="testShare()">分享内容</button>
            </div>
            <div id="systemResult" class="result"></div>
        </div>
        
        <!-- 标题栏控制测试 -->
        <div class="section" id="titlebar">
            <div class="section-title">📋 标题栏控制</div>
            <div class="button-group">
                <button class="btn" onclick="testSetTitle()">设置标题</button>
                <button class="btn" onclick="testSetRightButton()">设置右按钮</button>
                <button class="btn secondary" onclick="testHideRightButton()">隐藏右按钮</button>
                <button class="btn success" onclick="testCustomWebView()">自定义WebView</button>
            </div>
            <div class="input-group">
                <label>标题文本:</label>
                <input type="text" id="titleText" value="新标题" placeholder="输入标题">
            </div>
            <div class="input-group">
                <label>右按钮文本:</label>
                <input type="text" id="rightButtonText" value="打开页面" placeholder="输入右按钮文本">
            </div>
            <div class="input-group">
                <label>右按钮跳转URL:</label>
                <input type="text" id="rightButtonUrl" value="https://www.baidu.com" placeholder="输入右按钮要跳转的网址">
            </div>
            <div class="input-group">
                <label>自定义URL:</label>
                <input type="text" id="customUrl" value="https://www.baidu.com" placeholder="输入要打开的网址">
            </div>
            <div class="input-group">
                <label>页面标题:</label>
                <input type="text" id="customTitle" value="测试页面" placeholder="输入页面标题">
            </div>
            <div id="titleResult" class="result"></div>
        </div>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            正在执行操作...
        </div>
    </div>

    <script>
        // 全局变量
        let watchId = null;
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateTimestamp();
            checkAPIAvailability();
            handleUrlHash();
            showQuickNavIfNeeded();
        });
        
        function updateTimestamp() {
            document.getElementById('timestamp').textContent = 
                '加载时间: ' + new Date().toLocaleString();
        }
        
        function checkAPIAvailability() {
            const available = typeof window.NativeAPI !== 'undefined';
            const indicator = document.querySelector('.status-indicator');
            const status = document.querySelector('.status-bar div:first-child');
            
            if (available) {
                indicator.className = 'status-indicator online';
                status.innerHTML = '<span class="status-indicator online"></span>API状态: 已连接';
            } else {
                indicator.className = 'status-indicator offline';
                status.innerHTML = '<span class="status-indicator offline"></span>API状态: 未连接';
            }
        }
        
        function showLoading(show = true) {
            const loading = document.getElementById('loading');
            loading.className = show ? 'loading show' : 'loading';
        }
        
        function showResult(elementId, result, isError = false) {
            const element = document.getElementById(elementId);
            element.className = `result ${isError ? 'error' : 'success'}`;
            element.textContent = typeof result === 'object' 
                ? JSON.stringify(result, null, 2) 
                : result;
        }

        // 处理URL锚点，直接跳转到对应功能模块
        function handleUrlHash() {
            const hash = window.location.hash.substring(1);
            if (hash) {
                setTimeout(() => {
                    scrollToSection(hash);
                }, 500);
            }
        }

        // 滚动到指定功能模块
        function scrollToSection(sectionId) {
            const element = document.getElementById(sectionId);
            if (element) {
                element.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });

                // 高亮显示目标模块
                highlightSection(element);

                // 更新URL hash
                window.history.replaceState(null, null, '#' + sectionId);
            }
        }

        // 高亮显示功能模块
        function highlightSection(element) {
            // 移除之前的高亮
            const highlighted = document.querySelector('.section.highlighted');
            if (highlighted) {
                highlighted.classList.remove('highlighted');
            }

            // 添加高亮效果
            element.classList.add('highlighted');

            // 3秒后移除高亮
            setTimeout(() => {
                element.classList.remove('highlighted');
            }, 3000);
        }

        // 根据访问方式决定是否显示快速导航
        function showQuickNavIfNeeded() {
            const hash = window.location.hash;
            const quickNav = document.getElementById('quickNav');

            // 如果是从应用图标访问特定功能，显示快速导航
            if (hash && hash !== '#') {
                quickNav.style.display = 'block';
            } else {
                // 如果是访问完整页面，也显示快速导航（可选）
                quickNav.style.display = 'block';
            }
        }

        // 通用API调用函数
        async function callAPI(apiPath, params = {}, resultElementId) {
            showLoading(true);
            try {
                const pathParts = apiPath.split('.');
                let api = window.NativeAPI;
                
                for (const part of pathParts) {
                    api = api[part];
                    if (!api) {
                        throw new Error(`API ${apiPath} 不存在`);
                    }
                }
                
                const result = await api(params);
                showResult(resultElementId, result);
                return result;
            } catch (error) {
                showResult(resultElementId, `错误: ${error.message}`, true);
                throw error;
            } finally {
                showLoading(false);
            }
        }

        // 相机功能测试
        async function testTakePhoto() {
            const quality = parseInt(document.getElementById('imageQuality').value);
            const result = await callAPI('Camera.takePhoto', {
                quality: quality,
                maxWidth: 1024,
                maxHeight: 1024
            }, 'cameraResult');

            if (result && result.data && result.data.path) {
                const preview = document.getElementById('imagePreview');
                preview.src = 'file://' + result.data.path;
                preview.style.display = 'block';
            }
        }

        async function testRecordVideo() {
            await callAPI('Camera.recordVideo', {
                maxDuration: 30
            }, 'cameraResult');
        }

        async function testPickFromGallery() {
            const result = await callAPI('Camera.pickFromGallery', {
                type: 'image',
                quality: parseInt(document.getElementById('imageQuality').value)
            }, 'cameraResult');

            if (result && result.data && result.data.path) {
                const preview = document.getElementById('imagePreview');
                preview.src = 'file://' + result.data.path;
                preview.style.display = 'block';
            }
        }

        async function testPickMultiple() {
            await callAPI('Camera.pickMultipleFromGallery', {
                maxCount: 5,
                quality: parseInt(document.getElementById('imageQuality').value)
            }, 'cameraResult');
        }

        // 二维码功能测试
        async function testQRScan() {
            await callAPI('QRCode.scan', {
                prompt: '请将二维码放入扫描框内',
                timeout: 30000
            }, 'qrResult');
        }

        async function testQRGenerate() {
            const data = document.getElementById('qrData').value;
            const size = parseInt(document.getElementById('qrSize').value);

            const result = await callAPI('QRCode.generate', {
                data: data,
                size: size,
                errorCorrectionLevel: 'M'
            }, 'qrResult');

            if (result && result.data && result.data.base64) {
                const preview = document.getElementById('qrPreview');
                preview.src = result.data.base64;
                preview.style.display = 'block';
            }
        }

        async function testQRScanMultiple() {
            await callAPI('QRCode.scanMultiple', {
                maxCount: 5,
                autoStop: false
            }, 'qrResult');
        }

        // 蓝牙功能测试
        async function testBluetoothScan() {
            const result = await callAPI('Bluetooth.scanDevices', {
                timeout: 10000,
                includeUnknownDevices: false
            }, 'bluetoothResult');

            if (result && result.data && result.data.devices && result.data.devices.length > 0) {
                document.getElementById('bluetoothDeviceId').value = result.data.devices[0].id;
            }
        }

        async function testBluetoothConnect() {
            const deviceId = document.getElementById('bluetoothDeviceId').value;
            if (!deviceId) {
                showResult('bluetoothResult', '请先扫描并选择设备', true);
                return;
            }

            await callAPI('Bluetooth.connect', {
                deviceId: deviceId,
                timeout: 10000
            }, 'bluetoothResult');
        }

        async function testBluetoothDisconnect() {
            await callAPI('Bluetooth.disconnect', {}, 'bluetoothResult');
        }

        async function testBluetoothPrintText() {
            const text = document.getElementById('printText').value;
            await callAPI('Bluetooth.printText', {
                text: text,
                fontSize: 'normal',
                align: 'left',
                bold: false
            }, 'bluetoothResult');
        }

        async function testBluetoothPrintQR() {
            const data = document.getElementById('qrData').value;
            await callAPI('Bluetooth.printQRCode', {
                data: data,
                size: 200,
                align: 'center'
            }, 'bluetoothResult');
        }

        async function testBluetoothPrintReceipt() {
            await callAPI('Bluetooth.printReceipt', {
                title: '测试小票',
                items: [
                    { name: '商品A', price: '10.00', qty: '2' },
                    { name: '商品B', price: '15.50', qty: '1' }
                ],
                total: '35.50',
                footer: '感谢您的测试！'
            }, 'bluetoothResult');
        }

        // NFC功能测试
        async function testNFCAvailable() {
            await callAPI('NFC.isAvailable', {}, 'nfcResult');
        }

        async function testNFCScan() {
            await callAPI('NFC.startScan', {
                timeout: 30000
            }, 'nfcResult');
        }

        async function testNFCWrite() {
            const type = document.getElementById('nfcType').value;
            const data = document.getElementById('nfcData').value;

            await callAPI('NFC.writeTag', {
                type: type,
                data: data
            }, 'nfcResult');
        }

        // 定位功能测试
        async function testGetLocation() {
            await callAPI('Location.getCurrentPosition', {
                accuracy: 'high',
                timeout: 15000
            }, 'locationResult');
        }

        async function testGeocode() {
            const address = document.getElementById('address').value;
            await callAPI('Location.geocode', {
                address: address
            }, 'locationResult');
        }

        async function testWatchLocation() {
            try {
                watchId = await callAPI('Location.watchPosition', {
                    callback: (position) => {
                        showResult('locationResult', `位置更新: ${JSON.stringify(position, null, 2)}`);
                    },
                    accuracy: 'high',
                    distanceFilter: 10
                }, 'locationResult');
            } catch (error) {
                showResult('locationResult', `监听失败: ${error.message}`, true);
            }
        }

        async function testStopWatch() {
            if (watchId) {
                await callAPI('Location.clearWatch', { watchId: watchId }, 'locationResult');
                watchId = null;
            } else {
                showResult('locationResult', '没有正在监听的位置', true);
            }
        }

        // 设备信息测试
        async function testGetDeviceId() {
            await callAPI('Device.getDeviceId', {}, 'deviceResult');
        }

        async function testGetDeviceInfo() {
            await callAPI('Device.getDeviceInfo', {}, 'deviceResult');
        }

        async function testGetUserInfo() {
            await callAPI('Device.getUserInfo', {}, 'deviceResult');
        }

        async function testGetAppInfo() {
            await callAPI('Device.getAppInfo', {}, 'deviceResult');
        }

        // 系统功能测试
        async function testShowToast() {
            await callAPI('System.showToast', {
                message: '这是一个测试提示',
                duration: 2000
            }, 'systemResult');
        }

        async function testShowAlert() {
            await callAPI('System.showAlert', {
                title: '测试警告',
                message: '这是一个测试警告消息'
            }, 'systemResult');
        }

        async function testShowConfirm() {
            await callAPI('System.showConfirm', {
                title: '确认测试',
                message: '您确定要执行此操作吗？'
            }, 'systemResult');
        }

        async function testVibrate() {
            await callAPI('System.vibrate', {
                pattern: [100, 200, 100]
            }, 'systemResult');
        }

        async function testOpenUrl() {
            await callAPI('System.openUrl', {
                url: 'https://www.baidu.com'
            }, 'systemResult');
        }

        async function testShare() {
            await callAPI('System.shareContent', {
                text: '这是一个测试分享',
                url: 'https://example.com',
                title: '测试分享'
            }, 'systemResult');
        }

        // 标题栏控制测试
        async function testSetTitle() {
            const title = document.getElementById('titleText').value;
            await callAPI('TitleBar.setTitle', {
                title: title
            }, 'titleResult');
        }

        async function testSetRightButton() {
            const buttonText = document.getElementById('rightButtonText').value.trim();
            const buttonUrl = document.getElementById('rightButtonUrl').value.trim();

            if (!buttonText) {
                showResult('titleResult', {
                    success: false,
                    error: { message: '请输入右按钮文本' }
                });
                return;
            }

            // 如果输入了URL，验证URL格式
            let action = 'test'; // 默认动作
            if (buttonUrl) {
                try {
                    new URL(buttonUrl);
                    action = buttonUrl; // 使用URL作为动作
                } catch (e) {
                    showResult('titleResult', {
                        success: false,
                        error: { message: 'URL格式不正确，请输入完整的网址（如：https://www.example.com）' }
                    });
                    return;
                }
            }

            await callAPI('TitleBar.setRightButton', {
                text: buttonText,
                icon: 'link',
                action: action
            }, 'titleResult');
        }

        async function testHideRightButton() {
            await callAPI('TitleBar.hideRightButton', {}, 'titleResult');
        }

        // 自定义WebView测试
        async function testCustomWebView() {
            const url = document.getElementById('customUrl').value.trim();
            const title = document.getElementById('customTitle').value.trim();

            if (!url) {
                showResult('titleResult', {
                    success: false,
                    error: { message: '请输入有效的URL地址' }
                });
                return;
            }

            // 验证URL格式
            try {
                new URL(url);
            } catch (e) {
                showResult('titleResult', {
                    success: false,
                    error: { message: 'URL格式不正确，请输入完整的网址（如：https://www.example.com）' }
                });
                return;
            }

            await callAPI('System.openWebView', {
                url: url,
                title: title || '自定义页面'
            }, 'titleResult');
        }
    </script>
</body>
</html>
