<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备功能演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .demo-container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .demo-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        
        .demo-header h1 {
            font-size: 24px;
            margin-bottom: 8px;
        }
        
        .demo-header p {
            opacity: 0.9;
            font-size: 14px;
        }
        
        .demo-content {
            padding: 30px 20px;
        }
        
        .demo-section {
            margin-bottom: 30px;
        }
        
        .demo-section:last-child {
            margin-bottom: 0;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .section-title .icon {
            font-size: 24px;
            margin-right: 10px;
        }
        
        .demo-buttons {
            display: grid;
            gap: 12px;
        }
        
        .demo-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        .demo-btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .demo-btn:active {
            transform: translateY(0);
        }
        
        .demo-btn.secondary {
            background: #6c757d;
        }
        
        .demo-btn.secondary:hover {
            background: #5a6268;
        }
        
        .demo-btn.success {
            background: #28a745;
        }
        
        .demo-btn.success:hover {
            background: #218838;
        }
        
        .result-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 150px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-break: break-all;
        }
        
        .result-area.success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        
        .result-area.error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .loading.show {
            display: block;
        }
        
        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .quick-btn {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            border: 1px solid rgba(102, 126, 234, 0.3);
            padding: 12px;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
            text-align: center;
        }
        
        .quick-btn:hover {
            background: rgba(102, 126, 234, 0.2);
            border-color: #667eea;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>🚀 设备功能演示</h1>
            <p>体验WebView调用原生设备功能</p>
        </div>
        
        <div class="demo-content">
            <!-- 快速操作 -->
            <div class="demo-section">
                <div class="section-title">
                    <span class="icon">⚡</span>
                    快速体验
                </div>
                <div class="quick-actions">
                    <button class="quick-btn" onclick="quickTest('camera')">📷 拍照</button>
                    <button class="quick-btn" onclick="quickTest('qrcode')">📱 扫码</button>
                    <button class="quick-btn" onclick="quickTest('bluetooth')">🔵 蓝牙</button>
                    <button class="quick-btn" onclick="quickTest('nfc')">📡 NFC</button>
                    <button class="quick-btn" onclick="quickTest('device')">📱 设备ID</button>
                    <button class="quick-btn" onclick="quickTest('system')">💬 提示</button>
                </div>
            </div>
            
            <!-- 相机功能 -->
            <div class="demo-section">
                <div class="section-title">
                    <span class="icon">📷</span>
                    相机功能
                </div>
                <div class="demo-buttons">
                    <button class="demo-btn" onclick="testCamera()">
                        📸 拍照测试
                    </button>
                    <button class="demo-btn secondary" onclick="testGallery()">
                        🖼️ 相册选择
                    </button>
                </div>
            </div>
            
            <!-- 二维码功能 -->
            <div class="demo-section">
                <div class="section-title">
                    <span class="icon">📱</span>
                    二维码功能
                </div>
                <div class="demo-buttons">
                    <button class="demo-btn" onclick="testQRScan()">
                        🔍 扫描二维码
                    </button>
                    <button class="demo-btn secondary" onclick="testQRGenerate()">
                        ✨ 生成二维码
                    </button>
                </div>
            </div>
            
            <!-- 设备信息 -->
            <div class="demo-section">
                <div class="section-title">
                    <span class="icon">📱</span>
                    设备信息
                </div>
                <div class="demo-buttons">
                    <button class="demo-btn" onclick="testDeviceId()">
                        🆔 获取设备ID
                    </button>
                    <button class="demo-btn secondary" onclick="testDeviceInfo()">
                        ℹ️ 设备详情
                    </button>
                </div>
            </div>
            
            <!-- 蓝牙功能 -->
            <div class="demo-section">
                <div class="section-title">
                    <span class="icon">🔵</span>
                    蓝牙功能
                </div>
                <div class="demo-buttons">
                    <button class="demo-btn" onclick="testBluetoothScan()">
                        🔍 扫描设备
                    </button>
                    <button class="demo-btn secondary" onclick="testBluetoothConnect()" id="connectBtn" disabled>
                        🔗 连接设备
                    </button>
                    <button class="demo-btn secondary" onclick="testBluetoothDisconnect()">
                        ❌ 断开连接
                    </button>
                    <button class="demo-btn secondary" onclick="testBluetoothPrintText()">
                        🖨️ 打印文本
                    </button>
                    <button class="demo-btn secondary" onclick="testBluetoothPrintQR()">
                        📱 打印二维码
                    </button>
                    <button class="demo-btn secondary" onclick="testBluetoothPrintReceipt()">
                        🧾 打印小票
                    </button>
                </div>

                <!-- 设备选择区域 -->
                <div style="margin-top: 15px;">
                    <label style="font-weight: bold; color: #333;">设备ID:</label>
                    <input type="text" id="deviceIdInput" placeholder="选择设备后自动填入"
                           style="width: 100%; padding: 8px; margin-top: 5px; border: 1px solid #ddd; border-radius: 4px; font-family: monospace;">
                </div>

                <!-- 打印文本区域 -->
                <div style="margin-top: 15px;">
                    <label style="font-weight: bold; color: #333;">打印文本:</label>
                    <textarea id="printTextArea" placeholder="Hello World!"
                              style="width: 100%; height: 80px; padding: 8px; margin-top: 5px; border: 1px solid #ddd; border-radius: 4px; resize: vertical;">Hello World!</textarea>
                </div>
            </div>

            <!-- NFC功能 -->
            <div class="demo-section">
                <div class="section-title">
                    <span class="icon">📡</span>
                    NFC功能
                </div>
                <div class="demo-buttons">
                    <button class="demo-btn" onclick="testNFCAvailable()">
                        📡 检查可用性
                    </button>
                    <button class="demo-btn secondary" onclick="testNFCScan()">
                        🔍 扫描标签
                    </button>
                    <button class="demo-btn secondary" onclick="testNFCWrite()">
                        ✍️ 写入标签
                    </button>
                </div>
            </div>

            <!-- 系统功能 -->
            <div class="demo-section">
                <div class="section-title">
                    <span class="icon">⚙️</span>
                    系统功能
                </div>
                <div class="demo-buttons">
                    <button class="demo-btn success" onclick="testToast()">
                        💬 显示提示
                    </button>
                    <button class="demo-btn secondary" onclick="testVibrate()">
                        📳 震动反馈
                    </button>
                </div>
            </div>
            
            <!-- 结果显示 -->
            <div class="result-area" id="resultArea">
                点击上方按钮测试设备功能...
            </div>
            
            <!-- 加载状态 -->
            <div class="loading" id="loading">
                <div class="spinner"></div>
                正在调用设备功能...
            </div>
        </div>
    </div>

    <script>
        // 显示加载状态
        function showLoading(show = true) {
            const loading = document.getElementById('loading');
            loading.className = show ? 'loading show' : 'loading';
        }
        
        // 显示结果
        function showResult(result, isError = false) {
            const resultArea = document.getElementById('resultArea');
            resultArea.className = `result-area ${isError ? 'error' : 'success'}`;
            resultArea.textContent = typeof result === 'object' 
                ? JSON.stringify(result, null, 2) 
                : result;
        }
        
        // 通用API调用函数
        async function callAPI(apiPath, params = {}) {
            showLoading(true);
            try {
                const pathParts = apiPath.split('.');
                let api = window.NativeAPI;
                
                for (const part of pathParts) {
                    api = api[part];
                    if (!api) {
                        throw new Error(`API ${apiPath} 不存在`);
                    }
                }
                
                const result = await api(params);
                showResult(result);
                return result;
            } catch (error) {
                showResult(`错误: ${error.message}`, true);
                throw error;
            } finally {
                showLoading(false);
            }
        }
        
        // 快速测试
        async function quickTest(type) {
            switch (type) {
                case 'camera':
                    await testCamera();
                    break;
                case 'qrcode':
                    await testQRScan();
                    break;
                case 'bluetooth':
                    await testBluetoothScan();
                    break;
                case 'nfc':
                    await testNFCAvailable();
                    break;
                case 'device':
                    await testDeviceId();
                    break;
                case 'system':
                    await testToast();
                    break;
            }
        }
        
        // 相机测试
        async function testCamera() {
            await callAPI('Camera.takePhoto', {
                quality: 80,
                maxWidth: 1024,
                maxHeight: 1024
            });
        }
        
        // 相册选择
        async function testGallery() {
            await callAPI('Camera.pickFromGallery', {
                type: 'image',
                quality: 80
            });
        }
        
        // 二维码扫描
        async function testQRScan() {
            await callAPI('QRCode.scan', {
                prompt: '请将二维码放入扫描框内',
                timeout: 30000
            });
        }
        
        // 二维码生成
        async function testQRGenerate() {
            await callAPI('QRCode.generate', {
                data: 'https://example.com',
                size: 200,
                errorCorrectionLevel: 'M'
            });
        }
        
        // 获取设备ID
        async function testDeviceId() {
            await callAPI('Device.getDeviceId');
        }
        
        // 获取设备信息
        async function testDeviceInfo() {
            await callAPI('Device.getDeviceInfo');
        }
        
        // 显示提示
        async function testToast() {
            await callAPI('System.showToast', {
                message: '这是一个测试提示！',
                duration: 2000
            });
        }
        
        // 震动反馈
        async function testVibrate() {
            await callAPI('System.vibrate', {
                type: 'medium'
            });
        }

        // 蓝牙功能测试
        async function testBluetoothScan() {
            try {
                const result = await callAPI('Bluetooth.scanDevices', {
                    timeout: 10000,
                    includeUnknownDevices: false
                });

                if (result.success && result.data && result.data.devices) {
                    const devices = result.data.devices;
                    if (devices.length > 0) {
                        // 显示设备选择对话框
                        showDeviceSelectionDialog(devices);
                    } else {
                        alert('未发现蓝牙设备，请确保：\n1. 蓝牙已开启\n2. 目标设备处于可发现状态\n3. 距离足够近');
                    }
                } else {
                    alert('蓝牙扫描失败，请检查蓝牙权限和状态');
                }
            } catch (error) {
                console.error('蓝牙扫描错误:', error);
                alert('蓝牙扫描失败: ' + error.message);
            }
        }

        function showDeviceSelectionDialog(devices) {
            let deviceList = '发现 ' + devices.length + ' 个蓝牙设备：\n\n';
            devices.forEach((device, index) => {
                deviceList += `${index + 1}. ${device.name || 'Unknown Device'}\n`;
                deviceList += `   ID: ${device.id}\n`;
                deviceList += `   信号强度: ${device.rssi} dBm\n\n`;
            });

            const selection = prompt(deviceList + '请输入要连接的设备编号 (1-' + devices.length + '):');
            if (selection) {
                const deviceIndex = parseInt(selection) - 1;
                if (deviceIndex >= 0 && deviceIndex < devices.length) {
                    const selectedDevice = devices[deviceIndex];
                    document.getElementById('deviceIdInput').value = selectedDevice.id;
                    document.getElementById('connectBtn').disabled = false;
                    alert('已选择设备: ' + (selectedDevice.name || 'Unknown Device'));
                } else {
                    alert('无效的设备编号');
                }
            }
        }

        async function testBluetoothConnect() {
            const deviceId = document.getElementById('deviceIdInput').value;
            if (!deviceId) {
                alert('请先扫描并选择要连接的设备');
                return;
            }

            try {
                const result = await callAPI('Bluetooth.connect', {
                    deviceId: deviceId,
                    timeout: 10000
                });

                if (result.success) {
                    alert('蓝牙设备连接成功！');
                } else {
                    alert('蓝牙设备连接失败');
                }
            } catch (error) {
                alert('连接失败: ' + error.message);
            }
        }

        async function testBluetoothDisconnect() {
            const deviceId = document.getElementById('deviceIdInput').value;
            if (!deviceId) {
                alert('没有连接的设备');
                return;
            }

            try {
                await callAPI('Bluetooth.disconnect', {
                    deviceId: deviceId
                });
                alert('蓝牙设备已断开连接');
            } catch (error) {
                alert('断开连接失败: ' + error.message);
            }
        }

        async function testBluetoothPrintText() {
            const deviceId = document.getElementById('deviceIdInput').value;
            const text = document.getElementById('printTextArea').value;

            if (!deviceId) {
                alert('请先连接蓝牙设备');
                return;
            }

            try {
                await callAPI('Bluetooth.printText', {
                    deviceId: deviceId,
                    text: text + '\n',
                    fontSize: 'normal'
                });
                alert('打印文本成功');
            } catch (error) {
                alert('打印失败: ' + error.message);
            }
        }

        async function testBluetoothPrintQR() {
            const deviceId = document.getElementById('deviceIdInput').value;

            if (!deviceId) {
                alert('请先连接蓝牙设备');
                return;
            }

            try {
                await callAPI('Bluetooth.printQRCode', {
                    deviceId: deviceId,
                    data: 'https://example.com',
                    size: 200
                });
                alert('打印二维码成功');
            } catch (error) {
                alert('打印二维码失败: ' + error.message);
            }
        }

        async function testBluetoothPrintReceipt() {
            const deviceId = document.getElementById('deviceIdInput').value;

            if (!deviceId) {
                alert('请先连接蓝牙设备');
                return;
            }

            try {
                await callAPI('Bluetooth.printReceipt', {
                    deviceId: deviceId,
                    items: [
                        { name: '商品1', price: 10.00, quantity: 2 },
                        { name: '商品2', price: 15.50, quantity: 1 }
                    ],
                    total: 35.50
                });
                alert('打印小票成功');
            } catch (error) {
                alert('打印小票失败: ' + error.message);
            }
        }

        // NFC功能测试
        async function testNFCAvailable() {
            try {
                const result = await callAPI('NFC.isAvailable');
                if (result.success && result.data) {
                    const available = result.data.available;
                    if (available) {
                        alert('✅ NFC功能可用\n\n设备支持NFC功能，可以进行标签读写操作。');
                    } else {
                        alert('❌ NFC功能不可用\n\n可能的原因：\n1. 设备不支持NFC\n2. NFC功能未开启\n3. 系统版本不支持');
                    }
                } else {
                    alert('❓ 无法检测NFC状态');
                }
            } catch (error) {
                alert('检测NFC功能失败: ' + error.message);
            }
        }

        async function testNFCScan() {
            try {
                const result = await callAPI('NFC.startScan', {
                    timeout: 30000
                });

                if (result.success && result.data) {
                    alert('✅ NFC扫描成功！\n\n扫描到的数据:\n' + JSON.stringify(result.data, null, 2));
                } else {
                    alert('NFC扫描失败或超时');
                }
            } catch (error) {
                alert('NFC扫描失败: ' + error.message);
            }
        }

        async function testNFCWrite() {
            const writeData = document.getElementById('printTextArea').value || 'Hello NFC!';

            try {
                const result = await callAPI('NFC.writeTag', {
                    type: 'text',
                    data: writeData
                });

                if (result.success) {
                    alert('✅ NFC写入成功！\n\n已将文本写入NFC标签:\n' + writeData);
                } else {
                    alert('NFC写入失败');
                }
            } catch (error) {
                alert('NFC写入失败: ' + error.message);
            }
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查API可用性
            if (typeof window.NativeAPI === 'undefined') {
                showResult('⚠️ 原生API未加载，请在Flutter WebView中打开此页面', true);
            } else {
                showResult('✅ 原生API已就绪，可以开始测试设备功能');
            }
        });
    </script>
</body>
</html>
