<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权限调试工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 24px;
            margin-bottom: 8px;
        }
        
        .content {
            padding: 30px 20px;
        }
        
        .debug-section {
            margin-bottom: 25px;
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            background: #f8f9fa;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .debug-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            margin: 5px;
            width: 100%;
        }
        
        .debug-btn:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }
        
        .result {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #667eea;
        }
        
        .success {
            color: #28a745;
            border-color: #28a745;
        }
        
        .error {
            color: #dc3545;
            border-color: #dc3545;
        }
        
        .info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .step {
            margin: 8px 0;
            padding-left: 20px;
            position: relative;
        }
        
        .step::before {
            content: "•";
            position: absolute;
            left: 0;
            color: #2196f3;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 权限调试工具</h1>
            <p>诊断和修复权限问题</p>
        </div>
        
        <div class="content">
            <div class="info">
                <h3>🛠️ 调试说明</h3>
                <div class="step">此工具用于诊断权限请求问题</div>
                <div class="step">点击下方按钮查看详细的权限状态和错误信息</div>
                <div class="step">如果权限被拒绝，请到设置中手动开启</div>
            </div>
            
            <!-- 相机权限调试 -->
            <div class="debug-section">
                <div class="section-title">
                    📷 相机权限调试
                </div>
                <button class="debug-btn" onclick="debugCameraPermission()">
                    调试相机权限
                </button>
                <div id="camera-debug" class="result" style="display: none;"></div>
            </div>
            
            <!-- 相册权限调试 -->
            <div class="debug-section">
                <div class="section-title">
                    🖼️ 相册权限调试
                </div>
                <button class="debug-btn" onclick="debugPhotoPermission()">
                    调试相册权限
                </button>
                <div id="photo-debug" class="result" style="display: none;"></div>
            </div>
            
            <!-- 蓝牙权限调试 -->
            <div class="debug-section">
                <div class="section-title">
                    🔵 蓝牙权限调试
                </div>
                <button class="debug-btn" onclick="debugBluetoothPermission()">
                    调试蓝牙权限
                </button>
                <div id="bluetooth-debug" class="result" style="display: none;"></div>
            </div>
            
            <!-- 定位权限调试 -->
            <div class="debug-section">
                <div class="section-title">
                    📍 定位权限调试
                </div>
                <button class="debug-btn" onclick="debugLocationPermission()">
                    调试定位权限
                </button>
                <div id="location-debug" class="result" style="display: none;"></div>
            </div>
            
            <!-- 系统信息 -->
            <div class="debug-section">
                <div class="section-title">
                    📱 系统信息
                </div>
                <button class="debug-btn" onclick="getSystemInfo()">
                    获取系统信息
                </button>
                <div id="system-debug" class="result" style="display: none;"></div>
            </div>
            
            <div class="loading" id="loading">
                <div>⏳ 正在调试...</div>
            </div>
        </div>
    </div>

    <script>
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }
        
        function showResult(sectionId, message, isError = false) {
            const resultDiv = document.getElementById(sectionId + '-debug');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result ' + (isError ? 'error' : 'success');
            resultDiv.textContent = message;
            showLoading(false);
        }
        
        // 相机权限调试
        async function debugCameraPermission() {
            showLoading(true);
            
            try {
                const result = await window.NativeAPI.Camera.takePhoto({
                    quality: 80,
                    maxWidth: 1024,
                    maxHeight: 1024
                });
                
                showResult('camera', '✅ 相机权限正常\n\n详细信息:\n' + JSON.stringify(result, null, 2));
            } catch (error) {
                const debugInfo = `❌ 相机权限问题\n\n错误信息:\n${error.message}\n\n调试建议:\n`;
                
                let suggestions = '';
                if (error.message.includes('权限被拒绝')) {
                    suggestions += '1. 到 设置 > 隐私与安全性 > 相机 中开启权限\n';
                    suggestions += '2. 确保应用在相机权限列表中\n';
                    suggestions += '3. 重启应用后重试\n';
                } else if (error.message.includes('永久拒绝')) {
                    suggestions += '1. 必须到系统设置中手动开启相机权限\n';
                    suggestions += '2. 设置 > 隐私与安全性 > 相机 > 找到应用并开启\n';
                } else {
                    suggestions += '1. 检查设备是否支持相机功能\n';
                    suggestions += '2. 重启设备后重试\n';
                    suggestions += '3. 检查应用是否有相机权限配置\n';
                }
                
                showResult('camera', debugInfo + suggestions, true);
            }
        }
        
        // 相册权限调试
        async function debugPhotoPermission() {
            showLoading(true);
            
            try {
                const result = await window.NativeAPI.Camera.pickFromGallery({
                    type: 'image',
                    quality: 80
                });
                
                showResult('photo', '✅ 相册权限正常\n\n详细信息:\n' + JSON.stringify(result, null, 2));
            } catch (error) {
                const debugInfo = `❌ 相册权限问题\n\n错误信息:\n${error.message}\n\n调试建议:\n`;
                
                let suggestions = '';
                if (error.message.includes('权限被拒绝')) {
                    suggestions += '1. 到 设置 > 隐私与安全性 > 照片 中开启权限\n';
                    suggestions += '2. 选择"所有照片"或"选定的照片"\n';
                    suggestions += '3. 重启应用后重试\n';
                } else if (error.message.includes('永久拒绝')) {
                    suggestions += '1. 必须到系统设置中手动开启照片权限\n';
                    suggestions += '2. 设置 > 隐私与安全性 > 照片 > 找到应用并开启\n';
                } else {
                    suggestions += '1. 检查相册中是否有照片\n';
                    suggestions += '2. 重启设备后重试\n';
                }
                
                showResult('photo', debugInfo + suggestions, true);
            }
        }
        
        // 蓝牙权限调试
        async function debugBluetoothPermission() {
            showLoading(true);
            
            try {
                const result = await window.NativeAPI.Bluetooth.scanDevices({
                    timeout: 5000
                });
                
                showResult('bluetooth', '✅ 蓝牙权限正常\n\n详细信息:\n' + JSON.stringify(result, null, 2));
            } catch (error) {
                const debugInfo = `❌ 蓝牙权限问题\n\n错误信息:\n${error.message}\n\n调试建议:\n`;
                
                let suggestions = '';
                if (error.message.includes('权限被拒绝')) {
                    suggestions += '1. 到 设置 > 隐私与安全性 > 蓝牙 中开启权限\n';
                    suggestions += '2. 确保蓝牙功能已开启\n';
                    suggestions += '3. 重启应用后重试\n';
                } else if (error.message.includes('未开启')) {
                    suggestions += '1. 到 设置 > 蓝牙 中开启蓝牙功能\n';
                    suggestions += '2. 或从控制中心开启蓝牙\n';
                } else if (error.message.includes('不支持')) {
                    suggestions += '1. 设备可能不支持蓝牙功能\n';
                    suggestions += '2. 检查设备规格\n';
                } else {
                    suggestions += '1. 检查蓝牙是否正常工作\n';
                    suggestions += '2. 重启蓝牙功能\n';
                    suggestions += '3. 重启设备后重试\n';
                }
                
                showResult('bluetooth', debugInfo + suggestions, true);
            }
        }
        
        // 定位权限调试
        async function debugLocationPermission() {
            showLoading(true);
            
            try {
                const result = await window.NativeAPI.Location.getCurrentPosition({
                    accuracy: 'high',
                    timeout: 10000
                });
                
                showResult('location', '✅ 定位权限正常\n\n详细信息:\n' + JSON.stringify(result, null, 2));
            } catch (error) {
                const debugInfo = `❌ 定位权限问题\n\n错误信息:\n${error.message}\n\n调试建议:\n`;
                
                let suggestions = '';
                if (error.message.includes('权限被拒绝')) {
                    suggestions += '1. 到 设置 > 隐私与安全性 > 定位服务 中开启权限\n';
                    suggestions += '2. 确保定位服务已开启\n';
                    suggestions += '3. 选择"使用App时"或"始终"\n';
                } else if (error.message.includes('超时')) {
                    suggestions += '1. 到室外或窗边重试\n';
                    suggestions += '2. 检查GPS信号是否良好\n';
                    suggestions += '3. 重启定位服务\n';
                } else {
                    suggestions += '1. 检查定位服务是否开启\n';
                    suggestions += '2. 重启设备后重试\n';
                }
                
                showResult('location', debugInfo + suggestions, true);
            }
        }
        
        // 获取系统信息
        async function getSystemInfo() {
            showLoading(true);
            
            try {
                const deviceInfo = await window.NativeAPI.Device.getDeviceInfo();
                const isPhysical = await window.NativeAPI.Device.isPhysicalDevice();
                
                const systemInfo = {
                    设备信息: deviceInfo.data,
                    是否真实设备: isPhysical.data,
                    用户代理: navigator.userAgent,
                    平台: navigator.platform,
                    语言: navigator.language,
                    时间戳: new Date().toISOString()
                };
                
                showResult('system', '📱 系统信息\n\n' + JSON.stringify(systemInfo, null, 2));
            } catch (error) {
                showResult('system', '❌ 获取系统信息失败\n\n错误信息:\n' + error.message, true);
            }
        }
    </script>
</body>
</html>
