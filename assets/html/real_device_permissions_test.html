<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>真实设备权限测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 24px;
            margin-bottom: 8px;
        }
        
        .content {
            padding: 30px 20px;
        }
        
        .permission-section {
            margin-bottom: 25px;
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            background: #f8f9fa;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .permission-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            margin: 5px;
            width: 100%;
        }
        
        .permission-btn:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }
        
        .permission-btn.granted {
            background: #28a745;
        }
        
        .permission-btn.denied {
            background: #dc3545;
        }
        
        .permission-btn.pending {
            background: #ffc107;
            color: #333;
        }
        
        .result {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            font-family: monospace;
            font-size: 12px;
            max-height: 150px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #667eea;
        }
        
        .success {
            color: #28a745;
            border-color: #28a745;
        }
        
        .error {
            color: #dc3545;
            border-color: #dc3545;
        }
        
        .instructions {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .instructions h3 {
            color: #1976d2;
            margin-bottom: 10px;
        }
        
        .step {
            margin: 8px 0;
            padding-left: 20px;
            position: relative;
        }
        
        .step::before {
            content: "•";
            position: absolute;
            left: 0;
            color: #2196f3;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 真实设备权限测试</h1>
            <p>测试和请求设备权限</p>
        </div>
        
        <div class="content">
            <div class="instructions">
                <h3>📋 使用说明</h3>
                <div class="step">点击下方按钮测试各项设备权限</div>
                <div class="step">首次使用时会弹出权限请求对话框</div>
                <div class="step">请点击"允许"授予相应权限</div>
                <div class="step">如果权限被拒绝，请到设置中手动开启</div>
            </div>
            
            <!-- 相机权限测试 -->
            <div class="permission-section">
                <div class="section-title">
                    📷 相机权限测试
                </div>
                <button class="permission-btn" onclick="testCameraPermission()">
                    测试相机权限并拍照
                </button>
                <div id="camera-result" class="result" style="display: none;"></div>
            </div>
            
            <!-- 相册权限测试 -->
            <div class="permission-section">
                <div class="section-title">
                    🖼️ 相册权限测试
                </div>
                <button class="permission-btn" onclick="testPhotoPermission()">
                    测试相册权限并选择图片
                </button>
                <div id="photo-result" class="result" style="display: none;"></div>
            </div>
            
            <!-- 定位权限测试 -->
            <div class="permission-section">
                <div class="section-title">
                    📍 定位权限测试
                </div>
                <button class="permission-btn" onclick="testLocationPermission()">
                    测试定位权限并获取位置
                </button>
                <div id="location-result" class="result" style="display: none;"></div>
            </div>
            
            <!-- 蓝牙权限测试 -->
            <div class="permission-section">
                <div class="section-title">
                    🔵 蓝牙权限测试
                </div>
                <button class="permission-btn" onclick="testBluetoothPermission()">
                    测试蓝牙权限并扫描设备
                </button>
                <div id="bluetooth-result" class="result" style="display: none;"></div>
            </div>
            
            <!-- 系统功能测试 -->
            <div class="permission-section">
                <div class="section-title">
                    ⚙️ 系统功能测试
                </div>
                <button class="permission-btn" onclick="testSystemFeatures()">
                    测试系统提示和震动
                </button>
                <div id="system-result" class="result" style="display: none;"></div>
            </div>
            
            <div class="loading" id="loading">
                <div>⏳ 正在测试权限...</div>
            </div>
        </div>
    </div>

    <script>
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }
        
        function showResult(sectionId, message, isError = false) {
            const resultDiv = document.getElementById(sectionId + '-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result ' + (isError ? 'error' : 'success');
            resultDiv.textContent = message;
            showLoading(false);
        }
        
        function updateButtonStatus(buttonElement, status) {
            buttonElement.className = 'permission-btn ' + status;
            switch(status) {
                case 'granted':
                    buttonElement.textContent = buttonElement.textContent.replace('测试', '✅ 已授权 -');
                    break;
                case 'denied':
                    buttonElement.textContent = buttonElement.textContent.replace('测试', '❌ 被拒绝 -');
                    break;
                case 'pending':
                    buttonElement.textContent = buttonElement.textContent.replace('测试', '⏳ 请求中 -');
                    break;
            }
        }
        
        // 相机权限测试
        async function testCameraPermission() {
            const button = event.target;
            showLoading(true);
            updateButtonStatus(button, 'pending');
            
            try {
                const result = await window.NativeAPI.Camera.takePhoto({
                    quality: 80,
                    maxWidth: 1024,
                    maxHeight: 1024
                });
                
                updateButtonStatus(button, 'granted');
                showResult('camera', '相机权限测试成功！\n' + JSON.stringify(result, null, 2));
            } catch (error) {
                updateButtonStatus(button, 'denied');
                showResult('camera', '相机权限测试失败：\n' + error.message + '\n\n请到设置中授予相机权限', true);
            }
        }
        
        // 相册权限测试
        async function testPhotoPermission() {
            const button = event.target;
            showLoading(true);
            updateButtonStatus(button, 'pending');
            
            try {
                const result = await window.NativeAPI.Camera.pickFromGallery({
                    type: 'image',
                    quality: 80
                });
                
                updateButtonStatus(button, 'granted');
                showResult('photo', '相册权限测试成功！\n' + JSON.stringify(result, null, 2));
            } catch (error) {
                updateButtonStatus(button, 'denied');
                showResult('photo', '相册权限测试失败：\n' + error.message + '\n\n请到设置中授予相册权限', true);
            }
        }
        
        // 定位权限测试
        async function testLocationPermission() {
            const button = event.target;
            showLoading(true);
            updateButtonStatus(button, 'pending');
            
            try {
                const result = await window.NativeAPI.Location.getCurrentPosition({
                    accuracy: 'high',
                    timeout: 15000
                });
                
                updateButtonStatus(button, 'granted');
                showResult('location', '定位权限测试成功！\n' + JSON.stringify(result, null, 2));
            } catch (error) {
                updateButtonStatus(button, 'denied');
                showResult('location', '定位权限测试失败：\n' + error.message + '\n\n请到设置中授予定位权限', true);
            }
        }
        
        // 蓝牙权限测试
        async function testBluetoothPermission() {
            const button = event.target;
            showLoading(true);
            updateButtonStatus(button, 'pending');
            
            try {
                const result = await window.NativeAPI.Bluetooth.scanDevices({
                    timeout: 10000
                });
                
                updateButtonStatus(button, 'granted');
                showResult('bluetooth', '蓝牙权限测试成功！\n' + JSON.stringify(result, null, 2));
            } catch (error) {
                updateButtonStatus(button, 'denied');
                showResult('bluetooth', '蓝牙权限测试失败：\n' + error.message + '\n\n请到设置中授予蓝牙权限并确保蓝牙已开启', true);
            }
        }
        
        // 系统功能测试
        async function testSystemFeatures() {
            const button = event.target;
            showLoading(true);
            updateButtonStatus(button, 'pending');
            
            try {
                // 测试系统提示
                const toastResult = await window.NativeAPI.System.showToast({
                    message: '系统功能测试成功！',
                    duration: 2000
                });
                
                // 测试震动
                const vibrateResult = await window.NativeAPI.System.vibrate({
                    type: 'medium'
                });
                
                updateButtonStatus(button, 'granted');
                showResult('system', '系统功能测试成功！\n提示: ' + JSON.stringify(toastResult, null, 2) + '\n震动: ' + JSON.stringify(vibrateResult, null, 2));
            } catch (error) {
                updateButtonStatus(button, 'denied');
                showResult('system', '系统功能测试失败：\n' + error.message, true);
            }
        }
    </script>
</body>
</html>
