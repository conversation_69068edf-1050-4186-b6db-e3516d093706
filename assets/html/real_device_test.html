<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>真实设备功能测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 24px;
            margin-bottom: 8px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 14px;
        }
        
        .content {
            padding: 30px 20px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            background: #f8f9fa;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .test-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            margin: 5px;
        }
        
        .test-btn:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }
        
        .test-btn.secondary {
            background: #6c757d;
        }
        
        .test-btn.success {
            background: #28a745;
        }
        
        .test-btn.danger {
            background: #dc3545;
        }
        
        .result {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #667eea;
        }
        
        .success {
            color: #28a745;
            border-color: #28a745;
        }
        
        .error {
            color: #dc3545;
            border-color: #dc3545;
        }
        
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            margin-left: 10px;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status.pending {
            background: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 真实设备功能测试</h1>
            <p>测试WebView调用真实设备功能</p>
        </div>
        
        <div class="content">
            <!-- 相机功能测试 -->
            <div class="test-section">
                <div class="section-title">
                    📷 相机功能
                    <span id="camera-status" class="status pending">待测试</span>
                </div>
                <button class="test-btn" onclick="testTakePhoto()">拍照</button>
                <button class="test-btn secondary" onclick="testPickFromGallery()">选择图片</button>
                <button class="test-btn secondary" onclick="testRecordVideo()">录像</button>
                <div id="camera-result" class="result" style="display: none;"></div>
            </div>
            
            <!-- 定位功能测试 -->
            <div class="test-section">
                <div class="section-title">
                    📍 定位功能
                    <span id="location-status" class="status pending">待测试</span>
                </div>
                <button class="test-btn" onclick="testGetLocation()">获取位置</button>
                <button class="test-btn secondary" onclick="testGeocode()">地址解析</button>
                <div id="location-result" class="result" style="display: none;"></div>
            </div>
            
            <!-- 设备信息测试 -->
            <div class="test-section">
                <div class="section-title">
                    📱 设备信息
                    <span id="device-status" class="status pending">待测试</span>
                </div>
                <button class="test-btn" onclick="testGetDeviceId()">设备ID</button>
                <button class="test-btn secondary" onclick="testGetDeviceInfo()">设备详情</button>
                <button class="test-btn secondary" onclick="testIsPhysicalDevice()">物理设备检查</button>
                <div id="device-result" class="result" style="display: none;"></div>
            </div>
            
            <!-- 系统功能测试 -->
            <div class="test-section">
                <div class="section-title">
                    ⚙️ 系统功能
                    <span id="system-status" class="status pending">待测试</span>
                </div>
                <button class="test-btn success" onclick="testShowToast()">显示提示</button>
                <button class="test-btn secondary" onclick="testVibrate()">震动</button>
                <button class="test-btn secondary" onclick="testHapticFeedback()">触觉反馈</button>
                <div id="system-result" class="result" style="display: none;"></div>
            </div>
            
            <!-- 蓝牙功能测试 -->
            <div class="test-section">
                <div class="section-title">
                    🔵 蓝牙功能
                    <span id="bluetooth-status" class="status pending">待测试</span>
                </div>
                <button class="test-btn" onclick="testBluetoothScan()">扫描设备</button>
                <button class="test-btn secondary" onclick="testBluetoothIsEnabled()">检查状态</button>
                <div id="bluetooth-result" class="result" style="display: none;"></div>
            </div>
            
            <!-- NFC功能测试 -->
            <div class="test-section">
                <div class="section-title">
                    📡 NFC功能
                    <span id="nfc-status" class="status pending">待测试</span>
                </div>
                <button class="test-btn" onclick="testNFCAvailable()">检查可用性</button>
                <button class="test-btn secondary" onclick="testNFCScan()">扫描标签</button>
                <div id="nfc-result" class="result" style="display: none;"></div>
            </div>
            
            <div class="loading" id="loading">
                <div>⏳ 正在执行测试...</div>
            </div>
        </div>
    </div>

    <script>
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }
        
        function showResult(sectionId, message, isError = false) {
            const resultDiv = document.getElementById(sectionId + '-result');
            const statusDiv = document.getElementById(sectionId + '-status');
            
            resultDiv.style.display = 'block';
            resultDiv.className = 'result ' + (isError ? 'error' : 'success');
            resultDiv.textContent = message;
            
            statusDiv.className = 'status ' + (isError ? 'error' : 'success');
            statusDiv.textContent = isError ? '失败' : '成功';
            
            showLoading(false);
        }
        
        // 相机功能测试
        async function testTakePhoto() {
            showLoading(true);
            try {
                const result = await window.NativeAPI.Camera.takePhoto({
                    quality: 80,
                    maxWidth: 1024,
                    maxHeight: 1024
                });
                showResult('camera', '拍照成功:\n' + JSON.stringify(result, null, 2));
            } catch (error) {
                showResult('camera', '拍照失败:\n' + error.message, true);
            }
        }
        
        async function testPickFromGallery() {
            showLoading(true);
            try {
                const result = await window.NativeAPI.Camera.pickFromGallery({
                    type: 'image',
                    quality: 80
                });
                showResult('camera', '选择图片成功:\n' + JSON.stringify(result, null, 2));
            } catch (error) {
                showResult('camera', '选择图片失败:\n' + error.message, true);
            }
        }
        
        async function testRecordVideo() {
            showLoading(true);
            try {
                const result = await window.NativeAPI.Camera.recordVideo({
                    maxDuration: 30
                });
                showResult('camera', '录像成功:\n' + JSON.stringify(result, null, 2));
            } catch (error) {
                showResult('camera', '录像失败:\n' + error.message, true);
            }
        }
        
        // 定位功能测试
        async function testGetLocation() {
            showLoading(true);
            try {
                const result = await window.NativeAPI.Location.getCurrentPosition({
                    accuracy: 'high',
                    timeout: 15000
                });
                showResult('location', '获取位置成功:\n' + JSON.stringify(result, null, 2));
            } catch (error) {
                showResult('location', '获取位置失败:\n' + error.message, true);
            }
        }
        
        async function testGeocode() {
            showLoading(true);
            try {
                const result = await window.NativeAPI.Location.geocode({
                    address: '北京市朝阳区'
                });
                showResult('location', '地址解析成功:\n' + JSON.stringify(result, null, 2));
            } catch (error) {
                showResult('location', '地址解析失败:\n' + error.message, true);
            }
        }
        
        // 设备信息测试
        async function testGetDeviceId() {
            showLoading(true);
            try {
                const result = await window.NativeAPI.Device.getDeviceId();
                showResult('device', '获取设备ID成功:\n' + JSON.stringify(result, null, 2));
            } catch (error) {
                showResult('device', '获取设备ID失败:\n' + error.message, true);
            }
        }
        
        async function testGetDeviceInfo() {
            showLoading(true);
            try {
                const result = await window.NativeAPI.Device.getDeviceInfo();
                showResult('device', '获取设备信息成功:\n' + JSON.stringify(result, null, 2));
            } catch (error) {
                showResult('device', '获取设备信息失败:\n' + error.message, true);
            }
        }
        
        async function testIsPhysicalDevice() {
            showLoading(true);
            try {
                const result = await window.NativeAPI.Device.isPhysicalDevice();
                showResult('device', '物理设备检查成功:\n' + JSON.stringify(result, null, 2));
            } catch (error) {
                showResult('device', '物理设备检查失败:\n' + error.message, true);
            }
        }
        
        // 系统功能测试
        async function testShowToast() {
            showLoading(true);
            try {
                const result = await window.NativeAPI.System.showToast({
                    message: '这是一个真实的系统提示！',
                    duration: 2000
                });
                showResult('system', '显示提示成功:\n' + JSON.stringify(result, null, 2));
            } catch (error) {
                showResult('system', '显示提示失败:\n' + error.message, true);
            }
        }
        
        async function testVibrate() {
            showLoading(true);
            try {
                const result = await window.NativeAPI.System.vibrate({
                    type: 'medium'
                });
                showResult('system', '震动成功:\n' + JSON.stringify(result, null, 2));
            } catch (error) {
                showResult('system', '震动失败:\n' + error.message, true);
            }
        }
        
        async function testHapticFeedback() {
            showLoading(true);
            try {
                const result = await window.NativeAPI.System.hapticFeedback({
                    type: 'medium'
                });
                showResult('system', '触觉反馈成功:\n' + JSON.stringify(result, null, 2));
            } catch (error) {
                showResult('system', '触觉反馈失败:\n' + error.message, true);
            }
        }
        
        // 蓝牙功能测试
        async function testBluetoothScan() {
            showLoading(true);
            try {
                const result = await window.NativeAPI.Bluetooth.scanDevices({
                    timeout: 10000
                });
                showResult('bluetooth', '蓝牙扫描成功:\n' + JSON.stringify(result, null, 2));
            } catch (error) {
                showResult('bluetooth', '蓝牙扫描失败:\n' + error.message, true);
            }
        }
        
        async function testBluetoothIsEnabled() {
            showLoading(true);
            try {
                const result = await window.NativeAPI.Bluetooth.isEnabled();
                showResult('bluetooth', '蓝牙状态检查成功:\n' + JSON.stringify(result, null, 2));
            } catch (error) {
                showResult('bluetooth', '蓝牙状态检查失败:\n' + error.message, true);
            }
        }
        
        // NFC功能测试
        async function testNFCAvailable() {
            showLoading(true);
            try {
                const result = await window.NativeAPI.NFC.isAvailable();
                showResult('nfc', 'NFC可用性检查成功:\n' + JSON.stringify(result, null, 2));
            } catch (error) {
                showResult('nfc', 'NFC可用性检查失败:\n' + error.message, true);
            }
        }
        
        async function testNFCScan() {
            showLoading(true);
            try {
                const result = await window.NativeAPI.NFC.startScan({
                    timeout: 10000
                });
                showResult('nfc', 'NFC扫描成功:\n' + JSON.stringify(result, null, 2));
            } catch (error) {
                showResult('nfc', 'NFC扫描失败:\n' + error.message, true);
            }
        }
    </script>
</body>
</html>
