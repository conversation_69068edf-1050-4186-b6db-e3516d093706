<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备功能简单测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 24px;
            margin-bottom: 8px;
        }
        
        .content {
            padding: 30px 20px;
        }
        
        .test-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            width: 100%;
            margin-bottom: 15px;
        }
        
        .test-btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .result {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #667eea;
        }
        
        .success {
            color: #28a745;
        }
        
        .error {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 设备功能测试</h1>
            <p>验证WebView设备功能调用</p>
        </div>
        
        <div class="content">
            <button class="test-btn" onclick="testCamera()">
                📷 测试相机功能
            </button>
            
            <button class="test-btn" onclick="testQRCode()">
                📱 测试二维码扫描
            </button>
            
            <button class="test-btn" onclick="testDeviceInfo()">
                📱 测试设备信息
            </button>
            
            <button class="test-btn" onclick="testSystemToast()">
                💬 测试系统提示
            </button>
            
            <button class="test-btn" onclick="testVibration()">
                📳 测试震动反馈
            </button>
            
            <button class="test-btn" onclick="testBluetooth()">
                🔵 测试蓝牙功能
            </button>
            
            <button class="test-btn" onclick="testNFC()">
                📡 测试NFC功能
            </button>
            
            <button class="test-btn" onclick="testLocation()">
                📍 测试定位服务
            </button>
            
            <div class="loading" id="loading">
                <div>⏳ 正在执行测试...</div>
            </div>
            
            <div class="result" id="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }
        
        function showResult(message, isError = false) {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result ' + (isError ? 'error' : 'success');
            resultDiv.textContent = message;
            showLoading(false);
        }
        
        async function testCamera() {
            showLoading(true);
            try {
                const result = await window.NativeAPI.Camera.takePhoto({
                    quality: 80,
                    maxWidth: 1024,
                    maxHeight: 1024
                });
                showResult('相机测试成功:\n' + JSON.stringify(result, null, 2));
            } catch (error) {
                showResult('相机测试失败:\n' + error.message, true);
            }
        }
        
        async function testQRCode() {
            showLoading(true);
            try {
                const result = await window.NativeAPI.QRCode.scan({
                    prompt: '请将二维码放入扫描框内',
                    timeout: 30000
                });
                showResult('二维码测试成功:\n' + JSON.stringify(result, null, 2));
            } catch (error) {
                showResult('二维码测试失败:\n' + error.message, true);
            }
        }
        
        async function testDeviceInfo() {
            showLoading(true);
            try {
                const result = await window.NativeAPI.Device.getDeviceId();
                showResult('设备信息测试成功:\n' + JSON.stringify(result, null, 2));
            } catch (error) {
                showResult('设备信息测试失败:\n' + error.message, true);
            }
        }
        
        async function testSystemToast() {
            showLoading(true);
            try {
                const result = await window.NativeAPI.System.showToast({
                    message: '这是一个测试提示！',
                    duration: 2000
                });
                showResult('系统提示测试成功:\n' + JSON.stringify(result, null, 2));
            } catch (error) {
                showResult('系统提示测试失败:\n' + error.message, true);
            }
        }
        
        async function testVibration() {
            showLoading(true);
            try {
                const result = await window.NativeAPI.System.vibrate({
                    type: 'medium'
                });
                showResult('震动测试成功:\n' + JSON.stringify(result, null, 2));
            } catch (error) {
                showResult('震动测试失败:\n' + error.message, true);
            }
        }
        
        async function testBluetooth() {
            showLoading(true);
            try {
                const result = await window.NativeAPI.Bluetooth.scanDevices({
                    timeout: 5000
                });
                showResult('蓝牙测试成功:\n' + JSON.stringify(result, null, 2));
            } catch (error) {
                showResult('蓝牙测试失败:\n' + error.message, true);
            }
        }
        
        async function testNFC() {
            showLoading(true);
            try {
                const result = await window.NativeAPI.NFC.isAvailable();
                showResult('NFC测试成功:\n' + JSON.stringify(result, null, 2));
            } catch (error) {
                showResult('NFC测试失败:\n' + error.message, true);
            }
        }
        
        async function testLocation() {
            showLoading(true);
            try {
                const result = await window.NativeAPI.Location.getCurrentPosition({
                    accuracy: 'high',
                    timeout: 15000
                });
                showResult('定位测试成功:\n' + JSON.stringify(result, null, 2));
            } catch (error) {
                showResult('定位测试失败:\n' + error.message, true);
            }
        }
    </script>
</body>
</html>
